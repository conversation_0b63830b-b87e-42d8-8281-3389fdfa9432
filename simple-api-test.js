/**
 * Simple API Test Script for Multi-Supplier APIs
 * Manual step-by-step testing approach
 */

// Test configuration
const BASE_URL = 'http://localhost:3001';

// Test function
async function testAPI(method, endpoint, data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      // Add authentication cookie here if needed
      'Cookie': 'session-token=YOUR_TOKEN_HERE'
    }
  };

  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    options.body = JSON.stringify(data);
  }

  try {
    console.log(`Testing: ${method} ${endpoint}`);
    const response = await fetch(url, options);
    const responseData = await response.json();
    
    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(responseData, null, 2));
    console.log('---');
    
    return { success: response.ok, data: responseData, status: response.status };
  } catch (error) {
    console.log('Error:', error.message);
    return { success: false, error: error.message };
  }
}

// Manual test cases
async function runManualTests() {
  console.log('🧪 Manual API Testing Started');
  console.log('================================');

  // Test 1: Basic product listing (should work without auth for testing)
  await testAPI('GET', '/api/products?limit=2');

  // Test 2: Basic supplier listing
  await testAPI('GET', '/api/suppliers?limit=2');

  // Test 3: Test new ProductSupplier endpoint (will need auth)
  // Replace PRODUCT_ID with actual product ID
  // await testAPI('GET', '/api/products/PRODUCT_ID/suppliers');

  // Test 4: Test StockBatch endpoint (will need auth)
  // await testAPI('GET', '/api/stock-batches?limit=5');

  // Test 5: Test Supplier Analytics (will need auth)
  // await testAPI('GET', '/api/analytics/suppliers');

  console.log('🎉 Manual testing completed');
}

// Authentication test
async function testAuth() {
  console.log('🔐 Testing Authentication...');
  
  const loginData = {
    email: '<EMAIL>',
    password: 'password123'
  };

  const response = await testAPI('POST', '/api/auth/signin', loginData);
  
  if (response.success) {
    console.log('✅ Authentication successful');
    // Extract token from response if available
  } else {
    console.log('❌ Authentication failed');
  }
}

// Database connectivity test
async function testDatabaseConnectivity() {
  console.log('🗄️ Testing Database Connectivity...');
  
  // Test basic product count endpoint
  const response = await testAPI('GET', '/api/products/count');
  
  if (response.success) {
    console.log('✅ Database connectivity working');
    console.log('Product count:', response.data);
  } else {
    console.log('❌ Database connectivity issues');
  }
}

// Run specific tests
async function main() {
  console.log('🚀 Starting Simple API Tests...');
  
  // Test 1: Database connectivity
  await testDatabaseConnectivity();
  
  // Test 2: Authentication
  await testAuth();
  
  // Test 3: Basic API endpoints
  await runManualTests();
}

// Export for manual execution
if (typeof module !== 'undefined') {
  module.exports = { testAPI, testAuth, testDatabaseConnectivity, runManualTests };
}

// Instructions for manual testing
console.log(`
📋 MANUAL TESTING INSTRUCTIONS:

1. Make sure the development server is running on port 3001
2. Update the session token in the script if authentication is needed
3. Replace placeholder IDs with actual IDs from your database
4. Run specific test functions as needed

Example usage:
- testDatabaseConnectivity()
- testAuth()
- runManualTests()

To get actual IDs for testing:
1. First run: testAPI('GET', '/api/products?limit=1')
2. Copy a product ID from the response
3. Then test: testAPI('GET', '/api/products/PRODUCT_ID/suppliers')
`);

// Auto-run basic tests
main().catch(console.error);
