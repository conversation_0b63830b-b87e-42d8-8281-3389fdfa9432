
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  active: 'active'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.ActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  details: 'details',
  timestamp: 'timestamp'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  sku: 'sku',
  barcode: 'barcode',
  categoryId: 'categoryId',
  unitId: 'unitId',
  basePrice: 'basePrice',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  active: 'active',
  discountType: 'discountType',
  discountValue: 'discountValue',
  expiryDate: 'expiryDate',
  optionalPrice1: 'optionalPrice1',
  optionalPrice2: 'optionalPrice2',
  purchasePrice: 'purchasePrice',
  supplierId: 'supplierId'
};

exports.Prisma.ProductSupplierScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  supplierId: 'supplierId',
  supplierProductCode: 'supplierProductCode',
  supplierProductName: 'supplierProductName',
  purchasePrice: 'purchasePrice',
  minimumOrderQuantity: 'minimumOrderQuantity',
  leadTimeDays: 'leadTimeDays',
  isPreferred: 'isPreferred',
  isActive: 'isActive',
  lastOrderDate: 'lastOrderDate',
  lastPurchasePrice: 'lastPurchasePrice',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StockBatchScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  productSupplierId: 'productSupplierId',
  batchNumber: 'batchNumber',
  receivedDate: 'receivedDate',
  expiryDate: 'expiryDate',
  quantity: 'quantity',
  remainingQuantity: 'remainingQuantity',
  purchasePrice: 'purchasePrice',
  purchaseOrderId: 'purchaseOrderId',
  warehouseStockId: 'warehouseStockId',
  storeStockId: 'storeStockId',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UnitScalarFieldEnum = {
  id: 'id',
  name: 'name',
  abbreviation: 'abbreviation',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StoreStockScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  quantity: 'quantity',
  minThreshold: 'minThreshold',
  lastUpdated: 'lastUpdated',
  maxThreshold: 'maxThreshold'
};

exports.Prisma.WarehouseStockScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  quantity: 'quantity',
  lastUpdated: 'lastUpdated',
  maxThreshold: 'maxThreshold',
  minThreshold: 'minThreshold'
};

exports.Prisma.StockAdjustmentScalarFieldEnum = {
  id: 'id',
  date: 'date',
  productId: 'productId',
  batchId: 'batchId',
  storeStockId: 'storeStockId',
  warehouseStockId: 'warehouseStockId',
  previousQuantity: 'previousQuantity',
  newQuantity: 'newQuantity',
  adjustmentQuantity: 'adjustmentQuantity',
  reason: 'reason',
  notes: 'notes',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StockHistoryScalarFieldEnum = {
  id: 'id',
  date: 'date',
  productId: 'productId',
  productSupplierId: 'productSupplierId',
  batchId: 'batchId',
  storeStockId: 'storeStockId',
  warehouseStockId: 'warehouseStockId',
  previousQuantity: 'previousQuantity',
  newQuantity: 'newQuantity',
  changeQuantity: 'changeQuantity',
  source: 'source',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  notes: 'notes',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.SimpleStockTransferScalarFieldEnum = {
  id: 'id',
  date: 'date',
  productId: 'productId',
  quantity: 'quantity',
  fromStore: 'fromStore',
  toStore: 'toStore',
  status: 'status',
  notes: 'notes',
  requestedById: 'requestedById',
  approvedById: 'approvedById',
  approvedAt: 'approvedAt',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  transactionDate: 'transactionDate',
  cashierId: 'cashierId',
  customerId: 'customerId',
  subtotal: 'subtotal',
  discount: 'discount',
  tax: 'tax',
  total: 'total',
  paymentMethod: 'paymentMethod',
  paymentStatus: 'paymentStatus',
  cashReceived: 'cashReceived',
  changeAmount: 'changeAmount',
  dueDate: 'dueDate',
  approverId: 'approverId',
  approvedAt: 'approvedAt',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  drawerSessionId: 'drawerSessionId',
  terminalId: 'terminalId'
};

exports.Prisma.TransactionItemScalarFieldEnum = {
  id: 'id',
  transactionId: 'transactionId',
  productId: 'productId',
  batchId: 'batchId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  subtotal: 'subtotal'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  phone: 'phone',
  email: 'email',
  address: 'address',
  customerType: 'customerType',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PurchaseOrderScalarFieldEnum = {
  id: 'id',
  orderDate: 'orderDate',
  createdById: 'createdById',
  supplierId: 'supplierId',
  subtotal: 'subtotal',
  tax: 'tax',
  taxPercentage: 'taxPercentage',
  total: 'total',
  status: 'status',
  approvedById: 'approvedById',
  approvedAt: 'approvedAt',
  orderedAt: 'orderedAt',
  shippedAt: 'shippedAt',
  expectedDeliveryDate: 'expectedDeliveryDate',
  receivedAt: 'receivedAt',
  cancelledAt: 'cancelledAt',
  cancelledById: 'cancelledById',
  cancelReason: 'cancelReason',
  priority: 'priority',
  notes: 'notes',
  approvalNotes: 'approvalNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PurchaseOrderItemScalarFieldEnum = {
  id: 'id',
  purchaseOrderId: 'purchaseOrderId',
  productId: 'productId',
  productSupplierId: 'productSupplierId',
  supplierProductCode: 'supplierProductCode',
  quantity: 'quantity',
  receivedQuantity: 'receivedQuantity',
  unitPrice: 'unitPrice',
  subtotal: 'subtotal'
};

exports.Prisma.PurchaseOrderReceivingScalarFieldEnum = {
  id: 'id',
  purchaseOrderId: 'purchaseOrderId',
  receivedById: 'receivedById',
  receivedAt: 'receivedAt',
  notes: 'notes',
  discrepancyReason: 'discrepancyReason',
  createdAt: 'createdAt'
};

exports.Prisma.PurchaseOrderReceivingItemScalarFieldEnum = {
  id: 'id',
  purchaseOrderReceivingId: 'purchaseOrderReceivingId',
  purchaseOrderItemId: 'purchaseOrderItemId',
  receivedQuantity: 'receivedQuantity',
  discrepancyQuantity: 'discrepancyQuantity',
  discrepancyReason: 'discrepancyReason',
  notes: 'notes'
};

exports.Prisma.PurchaseOrderTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  supplierId: 'supplierId',
  taxPercentage: 'taxPercentage',
  notes: 'notes',
  isActive: 'isActive',
  createdById: 'createdById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PurchaseOrderTemplateItemScalarFieldEnum = {
  id: 'id',
  purchaseOrderTemplateId: 'purchaseOrderTemplateId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice'
};

exports.Prisma.SupplierScalarFieldEnum = {
  id: 'id',
  name: 'name',
  contactPerson: 'contactPerson',
  phone: 'phone',
  email: 'email',
  address: 'address',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReturnScalarFieldEnum = {
  id: 'id',
  returnDate: 'returnDate',
  transactionId: 'transactionId',
  customerId: 'customerId',
  reason: 'reason',
  total: 'total',
  status: 'status',
  disposition: 'disposition',
  dispositionReason: 'dispositionReason',
  addToSupplierQueue: 'addToSupplierQueue',
  supplierReturnQueueId: 'supplierReturnQueueId',
  customerResolution: 'customerResolution',
  customerResolutionNotes: 'customerResolutionNotes',
  customerResolutionProcessedAt: 'customerResolutionProcessedAt',
  customerResolutionProcessedBy: 'customerResolutionProcessedBy',
  awaitingRestock: 'awaitingRestock',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReturnItemScalarFieldEnum = {
  id: 'id',
  returnId: 'returnId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  subtotal: 'subtotal'
};

exports.Prisma.SupplierReturnScalarFieldEnum = {
  id: 'id',
  returnDate: 'returnDate',
  purchaseOrderId: 'purchaseOrderId',
  supplierId: 'supplierId',
  reason: 'reason',
  total: 'total',
  status: 'status',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SupplierReturnItemScalarFieldEnum = {
  id: 'id',
  supplierReturnId: 'supplierReturnId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  subtotal: 'subtotal'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  message: 'message',
  type: 'type',
  isRead: 'isRead',
  createdAt: 'createdAt',
  purchaseOrderId: 'purchaseOrderId',
  actionUrl: 'actionUrl',
  metadata: 'metadata'
};

exports.Prisma.SystemSettingScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StoreInfoScalarFieldEnum = {
  id: 'id',
  storeName: 'storeName',
  phone: 'phone',
  address: 'address',
  email: 'email',
  website: 'website',
  taxId: 'taxId',
  logoUrl: 'logoUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ConversationScalarFieldEnum = {
  id: 'id',
  title: 'title',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastActivity: 'lastActivity'
};

exports.Prisma.StarredConversationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  conversationId: 'conversationId',
  starredAt: 'starredAt'
};

exports.Prisma.ParticipantScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  conversationId: 'conversationId',
  joinedAt: 'joinedAt',
  leftAt: 'leftAt'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  conversationId: 'conversationId',
  senderId: 'senderId',
  receiverId: 'receiverId',
  content: 'content',
  isRead: 'isRead',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CashDrawerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  location: 'location',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.TerminalScalarFieldEnum = {
  id: 'id',
  name: 'name',
  ipAddress: 'ipAddress',
  macAddress: 'macAddress',
  location: 'location',
  description: 'description',
  isActive: 'isActive',
  drawerId: 'drawerId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DrawerSessionScalarFieldEnum = {
  id: 'id',
  drawerId: 'drawerId',
  userId: 'userId',
  terminalId: 'terminalId',
  businessDate: 'businessDate',
  openingBalance: 'openingBalance',
  expectedClosingBalance: 'expectedClosingBalance',
  actualClosingBalance: 'actualClosingBalance',
  discrepancy: 'discrepancy',
  openedAt: 'openedAt',
  closedAt: 'closedAt',
  status: 'status',
  notes: 'notes',
  shiftNumber: 'shiftNumber',
  previousSessionId: 'previousSessionId'
};

exports.Prisma.CashReconciliationScalarFieldEnum = {
  id: 'id',
  businessDate: 'businessDate',
  userId: 'userId',
  openingBalance: 'openingBalance',
  expectedAmount: 'expectedAmount',
  actualAmount: 'actualAmount',
  discrepancy: 'discrepancy',
  notes: 'notes',
  status: 'status',
  discrepancyCategory: 'discrepancyCategory',
  resolutionStatus: 'resolutionStatus',
  resolutionNotes: 'resolutionNotes',
  investigatedBy: 'investigatedBy',
  resolvedAt: 'resolvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CashAuditAlertScalarFieldEnum = {
  id: 'id',
  cashReconciliationId: 'cashReconciliationId',
  alertType: 'alertType',
  severity: 'severity',
  message: 'message',
  threshold: 'threshold',
  actualValue: 'actualValue',
  isResolved: 'isResolved',
  resolvedBy: 'resolvedBy',
  resolvedAt: 'resolvedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TemporaryPriceScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  value: 'value',
  type: 'type',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy'
};

exports.Prisma.RevenueTargetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  targetType: 'targetType',
  startDate: 'startDate',
  endDate: 'endDate',
  amount: 'amount',
  isActive: 'isActive',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  CASHIER: 'CASHIER',
  FINANCE_ADMIN: 'FINANCE_ADMIN',
  WAREHOUSE_ADMIN: 'WAREHOUSE_ADMIN',
  MARKETING: 'MARKETING',
  DEVELOPER: 'DEVELOPER'
};

exports.DiscountType = exports.$Enums.DiscountType = {
  FIXED: 'FIXED',
  PERCENTAGE: 'PERCENTAGE'
};

exports.BatchStatus = exports.$Enums.BatchStatus = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  RECALLED: 'RECALLED',
  SOLD_OUT: 'SOLD_OUT'
};

exports.AdjustmentReason = exports.$Enums.AdjustmentReason = {
  INVENTORY_COUNT: 'INVENTORY_COUNT',
  DAMAGED: 'DAMAGED',
  EXPIRED: 'EXPIRED',
  THEFT: 'THEFT',
  LOSS: 'LOSS',
  RETURN: 'RETURN',
  CORRECTION: 'CORRECTION',
  OTHER: 'OTHER'
};

exports.StockChangeSource = exports.$Enums.StockChangeSource = {
  PURCHASE: 'PURCHASE',
  SALE: 'SALE',
  ADJUSTMENT: 'ADJUSTMENT',
  TRANSFER: 'TRANSFER',
  RETURN: 'RETURN',
  INITIAL: 'INITIAL',
  OTHER: 'OTHER'
};

exports.TransferStatus = exports.$Enums.TransferStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  CASH: 'CASH',
  DEBIT: 'DEBIT',
  QRIS: 'QRIS'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  PARTIAL: 'PARTIAL',
  OVERDUE: 'OVERDUE',
  CANCELLED: 'CANCELLED'
};

exports.TransactionStatus = exports.$Enums.TransactionStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  VOIDED: 'VOIDED',
  RETURNED: 'RETURNED'
};

exports.CustomerType = exports.$Enums.CustomerType = {
  REGULAR: 'REGULAR',
  FRIEND: 'FRIEND',
  FAMILY: 'FAMILY'
};

exports.POStatus = exports.$Enums.POStatus = {
  DRAFT: 'DRAFT',
  PENDING_APPROVAL: 'PENDING_APPROVAL',
  APPROVED: 'APPROVED',
  ORDERED: 'ORDERED',
  SHIPPED: 'SHIPPED',
  PARTIALLY_RECEIVED: 'PARTIALLY_RECEIVED',
  RECEIVED: 'RECEIVED',
  CANCELLED: 'CANCELLED',
  OVERDUE: 'OVERDUE'
};

exports.ReturnStatus = exports.$Enums.ReturnStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  COMPLETED: 'COMPLETED',
  REJECTED: 'REJECTED'
};

exports.ReturnDisposition = exports.$Enums.ReturnDisposition = {
  RETURN_TO_STOCK: 'RETURN_TO_STOCK',
  DO_NOT_RETURN_TO_STOCK: 'DO_NOT_RETURN_TO_STOCK'
};

exports.CustomerResolution = exports.$Enums.CustomerResolution = {
  REPLACEMENT: 'REPLACEMENT',
  REFUND: 'REFUND',
  PENDING_REPLACEMENT: 'PENDING_REPLACEMENT',
  NONE: 'NONE'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  SYSTEM: 'SYSTEM',
  MESSAGE: 'MESSAGE',
  ALERT: 'ALERT',
  INFO: 'INFO',
  PURCHASE_ORDER_APPROVAL: 'PURCHASE_ORDER_APPROVAL',
  PURCHASE_ORDER_APPROVED: 'PURCHASE_ORDER_APPROVED',
  PURCHASE_ORDER_REJECTED: 'PURCHASE_ORDER_REJECTED',
  PURCHASE_ORDER_RECEIVED: 'PURCHASE_ORDER_RECEIVED'
};

exports.DrawerSessionStatus = exports.$Enums.DrawerSessionStatus = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED',
  RECONCILED: 'RECONCILED'
};

exports.ReconciliationStatus = exports.$Enums.ReconciliationStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.DiscrepancyCategory = exports.$Enums.DiscrepancyCategory = {
  COUNTING_ERROR: 'COUNTING_ERROR',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  THEFT_SUSPECTED: 'THEFT_SUSPECTED',
  CASH_SHORTAGE: 'CASH_SHORTAGE',
  CASH_SURPLUS: 'CASH_SURPLUS',
  REGISTER_ERROR: 'REGISTER_ERROR',
  TRAINING_ERROR: 'TRAINING_ERROR',
  PROCEDURAL_ERROR: 'PROCEDURAL_ERROR',
  UNKNOWN: 'UNKNOWN',
  OTHER: 'OTHER'
};

exports.ResolutionStatus = exports.$Enums.ResolutionStatus = {
  PENDING: 'PENDING',
  INVESTIGATING: 'INVESTIGATING',
  RESOLVED: 'RESOLVED',
  WRITTEN_OFF: 'WRITTEN_OFF',
  ESCALATED: 'ESCALATED',
  CLOSED: 'CLOSED'
};

exports.AuditAlertType = exports.$Enums.AuditAlertType = {
  LARGE_DISCREPANCY: 'LARGE_DISCREPANCY',
  FREQUENT_SHORTAGES: 'FREQUENT_SHORTAGES',
  PATTERN_DETECTED: 'PATTERN_DETECTED',
  THRESHOLD_EXCEEDED: 'THRESHOLD_EXCEEDED',
  UNUSUAL_ACTIVITY: 'UNUSUAL_ACTIVITY',
  COMPLIANCE_VIOLATION: 'COMPLIANCE_VIOLATION'
};

exports.AlertSeverity = exports.$Enums.AlertSeverity = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

exports.RevenueTargetType = exports.$Enums.RevenueTargetType = {
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  MONTHLY: 'MONTHLY',
  QUARTERLY: 'QUARTERLY',
  YEARLY: 'YEARLY'
};

exports.Prisma.ModelName = {
  User: 'User',
  Session: 'Session',
  ActivityLog: 'ActivityLog',
  Product: 'Product',
  ProductSupplier: 'ProductSupplier',
  StockBatch: 'StockBatch',
  Category: 'Category',
  Unit: 'Unit',
  StoreStock: 'StoreStock',
  WarehouseStock: 'WarehouseStock',
  StockAdjustment: 'StockAdjustment',
  StockHistory: 'StockHistory',
  SimpleStockTransfer: 'SimpleStockTransfer',
  Transaction: 'Transaction',
  TransactionItem: 'TransactionItem',
  Customer: 'Customer',
  PurchaseOrder: 'PurchaseOrder',
  PurchaseOrderItem: 'PurchaseOrderItem',
  PurchaseOrderReceiving: 'PurchaseOrderReceiving',
  PurchaseOrderReceivingItem: 'PurchaseOrderReceivingItem',
  PurchaseOrderTemplate: 'PurchaseOrderTemplate',
  PurchaseOrderTemplateItem: 'PurchaseOrderTemplateItem',
  Supplier: 'Supplier',
  Return: 'Return',
  ReturnItem: 'ReturnItem',
  SupplierReturn: 'SupplierReturn',
  SupplierReturnItem: 'SupplierReturnItem',
  Notification: 'Notification',
  SystemSetting: 'SystemSetting',
  StoreInfo: 'StoreInfo',
  Conversation: 'Conversation',
  StarredConversation: 'StarredConversation',
  Participant: 'Participant',
  Message: 'Message',
  CashDrawer: 'CashDrawer',
  Terminal: 'Terminal',
  DrawerSession: 'DrawerSession',
  CashReconciliation: 'CashReconciliation',
  CashAuditAlert: 'CashAuditAlert',
  TemporaryPrice: 'TemporaryPrice',
  RevenueTarget: 'RevenueTarget'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
