import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/auth';
import { prisma } from '@/auth';
import { z } from 'zod';
import { POStatus, POStatusChangeReason } from '@prisma/client';
import { isValidTransition } from '@/lib/po-status-management';
import { createPOStatusNotifications } from '@/lib/notifications';

// Status transition schema
const statusTransitionSchema = z.object({
  toStatus: z.enum([
    'DRAFT',
    'PENDING_APPROVAL',
    'APPROVED',
    'ORDERED',
    'PARTIALLY_RECEIVED',
    'RECEIVED',
    'CANCELLED',
    'OVERDUE',
    'ON_HOLD',
    'EXPEDITED',
  ]),
  reason: z.enum([
    'CREATED',
    'SUBMITTED_FOR_APPROVAL',
    'APPROVED',
    'REJECTED',
    'ORDERED_FROM_SUPPLIER',
    'DELIVERY_SCHEDULED',
    'PARTIALLY_RECEIVED',
    'FULLY_RECEIVED',
    'CANCELLED_BY_USER',
    'CANCELLED_BY_SUPPLIER',
    'PUT_ON_HOLD',
    'REMOVED_FROM_HOLD',
    'MARKED_OVERDUE',
    'EXPEDITED',
    'DELIVERY_DELAYED',
    'SUPPLIER_ISSUE',
    'BUDGET_CONSTRAINT',
    'INVENTORY_FULL',
    'OTHER',
  ]),
  notes: z.string().optional(),
  expectedDeliveryDate: z.string().datetime().optional().nullable(),
  holdReason: z.string().optional().nullable(),
  holdUntil: z.string().datetime().optional().nullable(),
});

// POST /api/purchase-orders/[id]/status-transition - Change PO status with validation
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = params;
    const body = await request.json();
    const validatedData = statusTransitionSchema.parse(body);

    // Get current PO
    const existingPO = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        supplier: true,
        items: true,
      },
    });

    if (!existingPO) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Validate transition
    if (!isValidTransition(existingPO.status, validatedData.toStatus, auth.user.role)) {
      return NextResponse.json({
        error: `Invalid status transition from ${existingPO.status} to ${validatedData.toStatus}`,
      }, { status: 400 });
    }

    // Perform the status transition in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Prepare update data
      const updateData: any = {
        status: validatedData.toStatus,
        updatedAt: new Date(),
      };

      // Handle specific status transitions
      if (validatedData.toStatus === 'APPROVED' && existingPO.status === 'PENDING_APPROVAL') {
        updateData.approvedById = auth.user.id;
        updateData.approvedAt = new Date();
      }

      if (validatedData.toStatus === 'RECEIVED') {
        updateData.receivedAt = new Date();
        updateData.actualDeliveryDate = new Date();
      }

      if (validatedData.expectedDeliveryDate) {
        updateData.expectedDeliveryDate = new Date(validatedData.expectedDeliveryDate);
      }

      if (validatedData.toStatus === 'ON_HOLD') {
        updateData.holdReason = validatedData.holdReason;
        if (validatedData.holdUntil) {
          updateData.holdUntil = new Date(validatedData.holdUntil);
        }
      } else {
        // Clear hold fields when not on hold
        updateData.holdReason = null;
        updateData.holdUntil = null;
      }

      // Update the purchase order
      const updatedPO = await tx.purchaseOrder.update({
        where: { id },
        data: updateData,
        include: {
          supplier: true,
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          approvedBy: {
            select: { id: true, name: true, email: true },
          },
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      // Create status history record
      await tx.pOStatusHistory.create({
        data: {
          purchaseOrderId: id,
          fromStatus: existingPO.status,
          toStatus: validatedData.toStatus,
          reason: validatedData.reason,
          notes: validatedData.notes,
          changedById: auth.user.id,
        },
      });

      return updatedPO;
    });

    // Create notifications for status changes
    try {
      await createPOStatusNotifications(id, validatedData.toStatus, auth.user.id);
    } catch (notificationError) {
      console.error('Error creating status notifications:', notificationError);
      // Don't fail the request if notifications fail
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: 'CHANGE_PO_STATUS',
        details: `Changed PO status from ${existingPO.status} to ${validatedData.toStatus} for supplier: ${existingPO.supplier.name}`,
      },
    });

    return NextResponse.json(result);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors,
      }, { status: 400 });
    }

    console.error('Error changing PO status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
