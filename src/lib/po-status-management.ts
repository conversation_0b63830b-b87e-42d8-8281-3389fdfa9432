/**
 * Enhanced Purchase Order Status Management System
 * Implements state machine logic for PO status transitions with validation
 */

import { POStatus, POPriority, POStatusChangeReason } from '@prisma/client';

export type { POStatus, POPriority, POStatusChangeReason };

export interface POStatusTransition {
  from: POStatus;
  to: POStatus;
  label: string;
  description: string;
  requiresApproval?: boolean;
  requiresReason?: boolean;
  allowedRoles?: string[];
}

export interface POStatusInfo {
  status: POStatus;
  label: string;
  description: string;
  color: string;
  bgColor: string;
  icon: string;
  category: 'draft' | 'pending' | 'active' | 'completed' | 'cancelled';
  progress: number; // 0-100
}

// Enhanced status information with detailed metadata
export const PO_STATUS_INFO: Record<POStatus, POStatusInfo> = {
  DRAFT: {
    status: 'DRAFT',
    label: 'Draft',
    description: 'Purchase order is being created',
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    icon: 'FileEdit',
    category: 'draft',
    progress: 10,
  },
  PENDING_APPROVAL: {
    status: 'PENDING_APPROVAL',
    label: 'Pending Approval',
    description: 'Waiting for management approval',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    icon: 'Clock',
    category: 'pending',
    progress: 25,
  },
  APPROVED: {
    status: 'APPROVED',
    label: 'Approved',
    description: 'Approved and ready to order',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    icon: 'CheckCircle',
    category: 'active',
    progress: 40,
  },
  ORDERED: {
    status: 'ORDERED',
    label: 'Ordered',
    description: 'Order sent to supplier',
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    icon: 'Send',
    category: 'active',
    progress: 55,
  },
  PARTIALLY_RECEIVED: {
    status: 'PARTIALLY_RECEIVED',
    label: 'Partially Received',
    description: 'Some items have been received',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    icon: 'PackageOpen',
    category: 'active',
    progress: 85,
  },
  RECEIVED: {
    status: 'RECEIVED',
    label: 'Received',
    description: 'All items have been received',
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    icon: 'PackageCheck',
    category: 'completed',
    progress: 100,
  },
  CANCELLED: {
    status: 'CANCELLED',
    label: 'Cancelled',
    description: 'Purchase order has been cancelled',
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    icon: 'XCircle',
    category: 'cancelled',
    progress: 0,
  },
  OVERDUE: {
    status: 'OVERDUE',
    label: 'Overdue',
    description: 'Expected delivery date has passed',
    color: 'text-red-700',
    bgColor: 'bg-red-200',
    icon: 'AlertTriangle',
    category: 'active',
    progress: 60,
  },
  ON_HOLD: {
    status: 'ON_HOLD',
    label: 'On Hold',
    description: 'Temporarily suspended',
    color: 'text-gray-700',
    bgColor: 'bg-gray-200',
    icon: 'Pause',
    category: 'active',
    progress: 30,
  },
  EXPEDITED: {
    status: 'EXPEDITED',
    label: 'Expedited',
    description: 'High priority, expedited processing',
    color: 'text-red-700',
    bgColor: 'bg-red-200',
    icon: 'Zap',
    category: 'active',
    progress: 75,
  },
};

// Valid status transitions with business rules
export const PO_STATUS_TRANSITIONS: POStatusTransition[] = [
  // From DRAFT
  {
    from: 'DRAFT',
    to: 'PENDING_APPROVAL',
    label: 'Submit for Approval',
    description: 'Submit the purchase order for management approval',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'DRAFT',
    to: 'CANCELLED',
    label: 'Cancel',
    description: 'Cancel the draft purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },

  // From PENDING_APPROVAL
  {
    from: 'PENDING_APPROVAL',
    to: 'APPROVED',
    label: 'Approve',
    description: 'Approve the purchase order',
    requiresApproval: true,
    allowedRoles: ['SUPER_ADMIN'],
  },
  {
    from: 'PENDING_APPROVAL',
    to: 'DRAFT',
    label: 'Return to Draft',
    description: 'Return to draft for modifications',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN'],
  },
  {
    from: 'PENDING_APPROVAL',
    to: 'CANCELLED',
    label: 'Reject',
    description: 'Reject the purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN'],
  },

  // From APPROVED
  {
    from: 'APPROVED',
    to: 'ORDERED',
    label: 'Send Order',
    description: 'Send the order to the supplier',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'APPROVED',
    to: 'ON_HOLD',
    label: 'Put on Hold',
    description: 'Temporarily suspend the purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'APPROVED',
    to: 'EXPEDITED',
    label: 'Mark as Expedited',
    description: 'Mark as high priority for expedited processing',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'APPROVED',
    to: 'CANCELLED',
    label: 'Cancel',
    description: 'Cancel the approved purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN'],
  },

  // From ORDERED
  {
    from: 'ORDERED',
    to: 'PARTIALLY_RECEIVED',
    label: 'Receive Items',
    description: 'Start receiving items (partial)',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'ORDERED',
    to: 'RECEIVED',
    label: 'Mark as Received',
    description: 'Mark all items as received',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'ORDERED',
    to: 'OVERDUE',
    label: 'Mark as Overdue',
    description: 'Expected delivery date has passed',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'ORDERED',
    to: 'ON_HOLD',
    label: 'Put on Hold',
    description: 'Temporarily suspend the order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'ORDERED',
    to: 'EXPEDITED',
    label: 'Mark as Expedited',
    description: 'Mark as high priority for expedited processing',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'ORDERED',
    to: 'CANCELLED',
    label: 'Cancel',
    description: 'Cancel the ordered purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN'],
  },

  // From ON_HOLD
  {
    from: 'ON_HOLD',
    to: 'APPROVED',
    label: 'Remove from Hold',
    description: 'Remove hold and return to approved status',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'ON_HOLD',
    to: 'ORDERED',
    label: 'Send Order',
    description: 'Remove hold and send order to supplier',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'ON_HOLD',
    to: 'CANCELLED',
    label: 'Cancel',
    description: 'Cancel the held purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN'],
  },

  // From EXPEDITED
  {
    from: 'EXPEDITED',
    to: 'PARTIALLY_RECEIVED',
    label: 'Receive Items',
    description: 'Start receiving expedited items',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'EXPEDITED',
    to: 'RECEIVED',
    label: 'Mark as Received',
    description: 'Mark all expedited items as received',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'EXPEDITED',
    to: 'OVERDUE',
    label: 'Mark as Overdue',
    description: 'Expected delivery date has passed',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'EXPEDITED',
    to: 'ON_HOLD',
    label: 'Put on Hold',
    description: 'Temporarily suspend the expedited order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'EXPEDITED',
    to: 'CANCELLED',
    label: 'Cancel',
    description: 'Cancel the expedited purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN'],
  },

  // From PARTIALLY_RECEIVED
  {
    from: 'PARTIALLY_RECEIVED',
    to: 'RECEIVED',
    label: 'Complete Receipt',
    description: 'Mark all remaining items as received',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'PARTIALLY_RECEIVED',
    to: 'OVERDUE',
    label: 'Mark as Overdue',
    description: 'Expected delivery date has passed',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },

  // From OVERDUE
  {
    from: 'OVERDUE',
    to: 'PARTIALLY_RECEIVED',
    label: 'Receive Items',
    description: 'Start receiving overdue items',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'OVERDUE',
    to: 'RECEIVED',
    label: 'Mark as Received',
    description: 'Mark all overdue items as received',
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'OVERDUE',
    to: 'ON_HOLD',
    label: 'Put on Hold',
    description: 'Temporarily suspend the overdue order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'],
  },
  {
    from: 'OVERDUE',
    to: 'CANCELLED',
    label: 'Cancel',
    description: 'Cancel the overdue purchase order',
    requiresReason: true,
    allowedRoles: ['SUPER_ADMIN'],
  },
];

/**
 * Get valid transitions from a given status
 */
export function getValidTransitions(currentStatus: POStatus, userRole: string): POStatusTransition[] {
  return PO_STATUS_TRANSITIONS.filter(
    (transition) =>
      transition.from === currentStatus &&
      (!transition.allowedRoles || transition.allowedRoles.includes(userRole))
  );
}

/**
 * Check if a status transition is valid
 */
export function isValidTransition(
  from: POStatus,
  to: POStatus,
  userRole: string
): boolean {
  const validTransitions = getValidTransitions(from, userRole);
  return validTransitions.some((transition) => transition.to === to);
}

/**
 * Get status information
 */
export function getStatusInfo(status: POStatus): POStatusInfo {
  return PO_STATUS_INFO[status];
}

/**
 * Calculate if a PO is overdue based on expected delivery date
 */
export function calculateOverdueStatus(
  currentStatus: POStatus,
  expectedDeliveryDate: Date | null,
  receivedAt: Date | null
): POStatus {
  if (receivedAt || currentStatus === 'RECEIVED' || currentStatus === 'CANCELLED') {
    return currentStatus;
  }

  if (
    expectedDeliveryDate &&
    new Date() > expectedDeliveryDate &&
    ['ORDERED', 'EXPEDITED', 'PARTIALLY_RECEIVED'].includes(currentStatus)
  ) {
    return 'OVERDUE';
  }

  return currentStatus;
}

/**
 * Get priority information
 */
export const PO_PRIORITY_INFO: Record<POPriority, { label: string; color: string; bgColor: string }> = {
  LOW: {
    label: 'Low',
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
  },
  NORMAL: {
    label: 'Normal',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  HIGH: {
    label: 'High',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
  },
  URGENT: {
    label: 'Urgent',
    color: 'text-red-600',
    bgColor: 'bg-red-100',
  },
};
