"use client";

import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";

interface CustomPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  siblingCount?: number;
}

export default function CustomPagination({
  currentPage,
  totalPages,
  onPageChange,
  siblingCount = 1,
}: CustomPaginationProps) {
  // If there's only 1 page, don't render pagination
  if (totalPages <= 1) return null;

  // Function to generate page numbers to display
  const generatePagination = () => {
    // Always show first page
    const firstPage = 1;
    // Always show last page
    const lastPage = totalPages;
    
    // Calculate range of pages to show around current page
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);
    
    // Determine whether to show dots
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 1;
    
    // Generate the array of page numbers to display
    const pageNumbers = [];
    
    // Always add first page
    pageNumbers.push(1);
    
    // Add left dots if needed
    if (shouldShowLeftDots) {
      pageNumbers.push(-1); // -1 represents dots
    } else if (firstPage + 1 <= totalPages) {
      // If we're not showing dots, show the second page
      pageNumbers.push(2);
    }
    
    // Add pages around current page
    for (let i = leftSiblingIndex; i <= rightSiblingIndex; i++) {
      // Skip if already added (first or second page)
      if (i !== 1 && i !== 2 && i !== totalPages && i !== totalPages - 1) {
        pageNumbers.push(i);
      }
    }
    
    // Add right dots if needed
    if (shouldShowRightDots) {
      pageNumbers.push(-2); // -2 represents dots
    } else if (lastPage - 1 > 0 && !pageNumbers.includes(lastPage - 1)) {
      // If we're not showing dots, show the second-to-last page
      pageNumbers.push(lastPage - 1);
    }
    
    // Always add last page if it's not already added
    if (lastPage !== 1 && !pageNumbers.includes(lastPage)) {
      pageNumbers.push(lastPage);
    }
    
    return pageNumbers;
  };

  const pageNumbers = generatePagination();

  return (
    <Pagination>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious 
            href="#" 
            onClick={(e) => {
              e.preventDefault();
              if (currentPage > 1) {
                onPageChange(currentPage - 1);
              }
            }}
            aria-disabled={currentPage === 1}
            tabIndex={currentPage === 1 ? -1 : undefined}
            className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>
        
        {pageNumbers.map((pageNumber, index) => {
          // Render dots
          if (pageNumber < 0) {
            return (
              <PaginationItem key={`dots-${index}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }
          
          // Render page number
          return (
            <PaginationItem key={pageNumber}>
              <PaginationLink 
                href="#" 
                isActive={pageNumber === currentPage}
                onClick={(e) => {
                  e.preventDefault();
                  onPageChange(pageNumber);
                }}
              >
                {pageNumber}
              </PaginationLink>
            </PaginationItem>
          );
        })}
        
        <PaginationItem>
          <PaginationNext 
            href="#" 
            onClick={(e) => {
              e.preventDefault();
              if (currentPage < totalPages) {
                onPageChange(currentPage + 1);
              }
            }}
            aria-disabled={currentPage === totalPages}
            tabIndex={currentPage === totalPages ? -1 : undefined}
            className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
