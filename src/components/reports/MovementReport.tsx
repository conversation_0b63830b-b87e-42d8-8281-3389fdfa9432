"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDate } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  ArrowDownCircle,
  ArrowUpCircle,
  ArrowRightCircle,
  Calendar,
  TrendingUp,
  TrendingDown,
  ExternalLink,
} from "lucide-react";

interface MovementReportProps {
  data: any;
  isLoading: boolean;
}

// Helper function to detect and extract transaction IDs from notes
function extractTransactionId(notes: string): string | null {
  if (!notes) return null;

  // Look for transaction ID patterns like "cmblg3g0028ulrkjfvwd5e" in notes
  // Transaction IDs are typically alphanumeric strings after "transaction"
  const transactionMatch = notes.match(/transaction\s+([a-zA-Z0-9]+)/i);
  return transactionMatch ? transactionMatch[1] : null;
}

// Component to render notes with clickable transaction IDs
function NotesCell({ notes }: { notes: string | null }) {
  const router = useRouter();

  if (!notes) return <span>-</span>;

  const transactionId = extractTransactionId(notes);

  if (!transactionId) {
    return <span>{notes}</span>;
  }

  // Split the notes to highlight the transaction ID
  const parts = notes.split(new RegExp(`(transaction\\s+${transactionId})`, "i"));

  return (
    <span>
      {parts.map((part, index) => {
        if (part.toLowerCase().includes("transaction") && part.includes(transactionId)) {
          return (
            <button
              key={index}
              onClick={() => router.push(`/transactions/${transactionId}`)}
              className="text-blue-600 hover:text-blue-800 underline inline-flex items-center gap-1 font-medium"
              title={`View transaction ${transactionId}`}
            >
              {part}
              <ExternalLink className="h-3 w-3" />
            </button>
          );
        }
        return <span key={index}>{part}</span>;
      })}
    </span>
  );
}

export function MovementReport({ data, isLoading }: MovementReportProps) {
  if (isLoading) {
    return <ReportSkeleton />;
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Movement Report</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const {
    summary,
    period,
    products,
    highestMovementProducts,
    lowestMovementProducts,
    generatedAt,
  } = data;

  return (
    <div className="space-y-6">
      <div className="text-sm text-muted-foreground">
        Report generated: {formatDate(generatedAt)}
      </div>

      {/* Period Information and Actions */}
      <div className="flex gap-4">
        <Card className="flex-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Report Period</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              From <span className="font-medium">{formatDate(period.startDate)}</span> to{" "}
              <span className="font-medium">{formatDate(period.endDate)}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-64">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Actions</CardTitle>
            <ArrowRightCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => (window.location.href = "/inventory/stock/simple-transfers")}
            >
              <ArrowRightCircle className="mr-2 h-4 w-4" />
              Stock Transfer
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <ArrowRightCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalProducts}</div>
            <p className="text-xs text-muted-foreground">{summary.totalMovements} movements</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock In</CardTitle>
            <ArrowDownCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.totalIn}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Out</CardTitle>
            <ArrowUpCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{summary.totalOut}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Change</CardTitle>
            <ArrowRightCircle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${summary.netChange > 0 ? "text-green-600" : summary.netChange < 0 ? "text-red-600" : ""}`}
            >
              {summary.netChange > 0 ? "+" : ""}
              {summary.netChange}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Highest and Lowest Movement Products */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Highest Movement Products */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Highest Movement Products</CardTitle>
              <CardDescription>Products with most stock changes</CardDescription>
            </div>
            <TrendingUp className="h-5 w-5 text-blue-500" />
          </CardHeader>
          <CardContent>
            {highestMovementProducts && highestMovementProducts.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="text-right">Movements</TableHead>
                    <TableHead className="text-right">Net Change</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {highestMovementProducts.map((product: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>{product.sku}</TableCell>
                      <TableCell className="text-right">{product.movementCount}</TableCell>
                      <TableCell className="text-right">
                        <span
                          className={
                            product.netChange > 0
                              ? "text-green-600"
                              : product.netChange < 0
                                ? "text-red-600"
                                : ""
                          }
                        >
                          {product.netChange > 0 ? "+" : ""}
                          {product.netChange}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="py-4 text-center text-muted-foreground">
                No movement data available
              </div>
            )}
          </CardContent>
        </Card>

        {/* Lowest Movement Products */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Lowest Movement Products</CardTitle>
              <CardDescription>Products with least stock changes</CardDescription>
            </div>
            <TrendingDown className="h-5 w-5 text-gray-500" />
          </CardHeader>
          <CardContent>
            {lowestMovementProducts && lowestMovementProducts.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="text-right">Movements</TableHead>
                    <TableHead className="text-right">Net Change</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {lowestMovementProducts.map((product: any, index: number) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>{product.sku}</TableCell>
                      <TableCell className="text-right">{product.movementCount}</TableCell>
                      <TableCell className="text-right">
                        <span
                          className={
                            product.netChange > 0
                              ? "text-green-600"
                              : product.netChange < 0
                                ? "text-red-600"
                                : ""
                          }
                        >
                          {product.netChange > 0 ? "+" : ""}
                          {product.netChange}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="py-4 text-center text-muted-foreground">
                No movement data available
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Products Movement */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Movements</CardTitle>
          <CardDescription>Detailed stock movements by product</CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="w-full">
            {products.map((productData: any, index: number) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger>
                  <div className="flex items-center justify-between w-full pr-4">
                    <div className="font-medium">{productData.product.name}</div>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-muted-foreground">
                        {productData.product.sku}
                      </span>
                      <Badge variant="outline" className="ml-2">
                        {productData.movements.length} movements
                      </Badge>
                      <span
                        className={`text-sm font-medium ${productData.netChange > 0 ? "text-green-600" : productData.netChange < 0 ? "text-red-600" : ""}`}
                      >
                        {productData.netChange > 0 ? "+" : ""}
                        {productData.netChange}
                      </span>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Source</TableHead>
                        <TableHead className="text-right">Previous</TableHead>
                        <TableHead className="text-right">Change</TableHead>
                        <TableHead className="text-right">New</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {productData.movements.map((movement: any, idx: number) => (
                        <TableRow key={idx}>
                          <TableCell>{formatDate(movement.date)}</TableCell>
                          <TableCell>{formatSource(movement.source)}</TableCell>
                          <TableCell className="text-right">{movement.previousQuantity}</TableCell>
                          <TableCell className="text-right">
                            <span
                              className={
                                movement.changeQuantity > 0
                                  ? "text-green-600"
                                  : movement.changeQuantity < 0
                                    ? "text-red-600"
                                    : ""
                              }
                            >
                              {movement.changeQuantity > 0 ? "+" : ""}
                              {movement.changeQuantity}
                            </span>
                          </TableCell>
                          <TableCell className="text-right">{movement.newQuantity}</TableCell>
                          <TableCell>{movement.user}</TableCell>
                          <TableCell>
                            <NotesCell notes={movement.notes} />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper function to format source
function formatSource(source: string): string {
  switch (source) {
    case "ADJUSTMENT":
      return "Manual Adjustment";
    case "SALE":
      return "Sale";
    case "PURCHASE":
      return "Purchase";
    case "TRANSFER":
      return "Transfer";
    case "RETURN":
      return "Return";
    default:
      return source;
  }
}

function ReportSkeleton() {
  return (
    <div className="space-y-6">
      <div className="text-sm text-muted-foreground">
        <Skeleton className="h-4 w-40" />
      </div>

      {/* Period Card Skeleton */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-5 w-64" />
        </CardContent>
      </Card>

      {/* Summary Cards Skeleton */}
      <div className="grid gap-4 md:grid-cols-4">
        {Array(4)
          .fill(0)
          .map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-24 mt-1" />
              </CardContent>
            </Card>
          ))}
      </div>

      {/* Highest and Lowest Movement Products Skeleton */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Highest Movement Products Skeleton */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <Skeleton className="h-6 w-40" />
              <Skeleton className="h-4 w-48 mt-1" />
            </div>
            <Skeleton className="h-5 w-5" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Skeleton className="h-10 w-full" />
              {Array(5)
                .fill(0)
                .map((_, i) => (
                  <Skeleton key={`high-${i}`} className="h-10 w-full" />
                ))}
            </div>
          </CardContent>
        </Card>

        {/* Lowest Movement Products Skeleton */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <Skeleton className="h-6 w-40" />
              <Skeleton className="h-4 w-48 mt-1" />
            </div>
            <Skeleton className="h-5 w-5" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Skeleton className="h-10 w-full" />
              {Array(5)
                .fill(0)
                .map((_, i) => (
                  <Skeleton key={`low-${i}`} className="h-10 w-full" />
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Accordion Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array(3)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
