"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Minus, Plus, Trash2 } from "lucide-react";
import { usePOSCart } from "@/contexts/POSCartContext";
import { usePOS } from "@/contexts/POSContext";
import { useEffect, useState, useRef } from "react";

export function ShoppingCart() {
  const { cartItems, updateCartItem, removeFromCart, clearCart, subtotal, totalDiscount, total } =
    usePOSCart();
  const { focusSearchInput } = usePOS();

  // State for tracking which discount field is being edited
  const [editingDiscountId, setEditingDiscountId] = useState<string | null>(null);
  const [tempDiscountValue, setTempDiscountValue] = useState<string>("");
  const discountInputRef = useRef<HTMLInputElement>(null);

  console.log("=== SHOPPING CART RENDER ===");
  console.log("Cart items:", cartItems);
  console.log("Cart length:", cartItems.length);
  console.log("Cart items detailed:", JSON.stringify(cartItems, null, 2));

  useEffect(() => {
    console.log("=== CART ITEMS CHANGED ===");
    console.log("New cart items:", cartItems);
    console.log("New cart length:", cartItems.length);
  }, [cartItems]);

  // Handle click outside to save discount edit
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        editingDiscountId &&
        discountInputRef.current &&
        !discountInputRef.current.contains(event.target as Node)
      ) {
        handleSaveDiscountEdit(editingDiscountId);
      }
    };

    if (editingDiscountId) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [editingDiscountId, tempDiscountValue]);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    updateCartItem(itemId, { quantity: newQuantity });
    // Auto-focus search input after quantity change
    focusSearchInput();
  };

  const handleDiscountChange = (itemId: string, newDiscount: number) => {
    if (newDiscount < 0) return;
    updateCartItem(itemId, { discount: newDiscount });
  };

  // Handle starting discount edit
  const handleStartDiscountEdit = (itemId: string, currentDiscount: number) => {
    setEditingDiscountId(itemId);
    setTempDiscountValue(currentDiscount.toString());
  };

  // Handle saving discount edit
  const handleSaveDiscountEdit = (itemId: string) => {
    const newDiscount = Math.round(parseFloat(tempDiscountValue) || 0);
    if (newDiscount >= 0) {
      handleDiscountChange(itemId, newDiscount);
    }
    setEditingDiscountId(null);
    setTempDiscountValue("");
    // Auto-focus search input after discount edit
    focusSearchInput();
  };

  // Handle canceling discount edit
  const handleCancelDiscountEdit = () => {
    setEditingDiscountId(null);
    setTempDiscountValue("");
    // Auto-focus search input after canceling discount edit
    focusSearchInput();
  };

  // Handle clear cart with refocus
  const handleClearCart = () => {
    clearCart();
    // Auto-focus search input after clearing cart
    focusSearchInput();
  };

  // Handle remove item with refocus
  const handleRemoveItem = (itemId: string) => {
    removeFromCart(itemId);
    // Auto-focus search input after removing item
    focusSearchInput();
  };

  // Handle key press in discount input
  const handleDiscountKeyPress = (e: React.KeyboardEvent, itemId: string) => {
    if (e.key === "Enter") {
      handleSaveDiscountEdit(itemId);
    } else if (e.key === "Escape") {
      handleCancelDiscountEdit();
    }
  };

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Shopping Cart</CardTitle>
            {cartItems.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearCart}
                className="text-red-600 hover:text-red-700"
              >
                Clear Cart
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          {cartItems.length === 0 ? (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <p className="text-lg">Cart is empty</p>
                <p className="text-sm">Search and add products to get started</p>
              </div>
            </div>
          ) : (
            <>
              <div className="flex-1 overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Discount</TableHead>
                      <TableHead>Subtotal</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cartItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-gray-500">per {item.unit}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            {item.hasTemporaryDiscount && item.originalPrice ? (
                              <>
                                <div className="text-xs text-gray-400 line-through">
                                  Rp {Number(item.originalPrice).toLocaleString("id-ID")}
                                </div>
                                <div className="font-medium text-green-600">
                                  Rp {Number(item.unitPrice).toLocaleString("id-ID")}
                                </div>
                                <div className="text-xs text-green-600">Temporary Price</div>
                              </>
                            ) : (
                              <div className="font-medium">
                                Rp {Number(item.unitPrice).toLocaleString("id-ID")}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              disabled={item.quantity <= 1}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center">{item.quantity}</span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          {editingDiscountId === item.id ? (
                            <Input
                              ref={discountInputRef}
                              type="number"
                              min="0"
                              step="1"
                              value={tempDiscountValue}
                              onChange={(e) => setTempDiscountValue(e.target.value)}
                              onKeyDown={(e) => handleDiscountKeyPress(e, item.id)}
                              className="w-20"
                              autoFocus
                            />
                          ) : (
                            <div
                              className="w-20 px-2 py-1 cursor-pointer hover:bg-gray-100 rounded text-center"
                              onClick={() => handleStartDiscountEdit(item.id, item.discount)}
                              title="Click to edit discount"
                            >
                              {item.discount > 0
                                ? `Rp ${Number(item.discount).toLocaleString("id-ID")}`
                                : "Rp 0"}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            Rp{" "}
                            {(
                              Number(item.subtotal) -
                              Number(item.discount) -
                              Number(item.autoDiscount)
                            ).toLocaleString("id-ID")}
                          </div>
                          {Number(item.autoDiscount) > 0 && (
                            <div className="text-xs text-green-600">
                              Auto: -Rp {Number(item.autoDiscount).toLocaleString("id-ID")}
                            </div>
                          )}
                          {item.hasTemporaryDiscount && item.originalPrice && (
                            <div className="text-xs text-green-600">
                              Saved: Rp{" "}
                              {(
                                (item.originalPrice - item.unitPrice) *
                                item.quantity
                              ).toLocaleString("id-ID")}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveItem(item.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Cart Summary */}
              <div className="border-t pt-4 mt-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>Rp {Number(subtotal).toLocaleString("id-ID")}</span>
                  </div>
                  <div className="flex justify-between text-green-600">
                    <span>Total Discount:</span>
                    <span>-Rp {Number(totalDiscount).toLocaleString("id-ID")}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t pt-2">
                    <span>Total:</span>
                    <span>Rp {Number(total).toLocaleString("id-ID")}</span>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
