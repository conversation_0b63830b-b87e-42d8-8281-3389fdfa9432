"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Search, Package } from "lucide-react";
import { usePOSCart, Product } from "@/contexts/POSCartContext";
import { usePOS } from "@/contexts/POSContext";

export function ProductSearch() {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isDropdownFocused, setIsDropdownFocused] = useState(false);
  const [isUserTyping, setIsUserTyping] = useState(false);
  const { addToCart } = usePOSCart();
  const { searchInputRef, focusSearchInput } = usePOS();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Refs to hold current state values for stable event handlers
  const stateRef = useRef({
    isDropdownFocused,
    selectedIndex,
    showDropdown,
    searchResults,
    searchTerm,
    isUserTyping,
  });

  // Update state ref whenever state changes
  useEffect(() => {
    stateRef.current = {
      isDropdownFocused,
      selectedIndex,
      showDropdown,
      searchResults,
      searchTerm,
      isUserTyping,
    };
  });

  // Note: Focus management is handled by the main POS page
  // This prevents conflicts with the centralized focus system

  // Handle click outside to close dropdown and manage focus
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;

      // Check if click is outside both search input and dropdown
      const isOutsideSearch = searchInputRef.current && !searchInputRef.current.contains(target);
      const isOutsideDropdown = !dropdownRef.current || !dropdownRef.current.contains(target);

      if (isOutsideSearch && isOutsideDropdown) {
        // Close dropdown if clicking outside
        if (showDropdown) {
          setShowDropdown(false);
          setSelectedIndex(-1);
        }

        // Auto-refocus search input when clicking anywhere else on the page
        if (
          !target.closest("button") &&
          !target.closest("input") &&
          !target.closest('[role="dialog"]') && // Don't refocus when modal is open
          !target.closest("[data-radix-dialog-content]") && // Radix dialog content
          !target.closest(".fixed.inset-0") && // Modal overlay
          !target.closest('[data-state="open"]') // Any open modal state
        ) {
          setTimeout(() => {
            if (searchInputRef.current) {
              searchInputRef.current.focus();
            }
          }, 100);
        }
      }
    };

    document.addEventListener("click", handleGlobalClick);
    return () => {
      document.removeEventListener("click", handleGlobalClick);
    };
  }, [showDropdown]);

  // Auto-refocus search input when drawer session is opened
  useEffect(() => {
    const handleDrawerSessionCreated = (event: CustomEvent) => {
      console.log(
        "ProductSearch: Drawer session created, auto-focusing search input",
        event.detail
      );
      setTimeout(() => {
        if (searchInputRef.current) {
          console.log("ProductSearch: Executing drawer opening auto-refocus");
          searchInputRef.current.focus();
        }
      }, 100);
    };

    console.log("ProductSearch: Adding drawer session created event listener");
    window.addEventListener("drawerSessionCreated", handleDrawerSessionCreated as EventListener);

    return () => {
      console.log("ProductSearch: Removing drawer session created event listener");
      window.removeEventListener(
        "drawerSessionCreated",
        handleDrawerSessionCreated as EventListener
      );
    };
  }, []);

  // Precise focus management based on search results and typing state
  useEffect(() => {
    if (showDropdown && searchResults.length > 0) {
      // If user is not currently typing, transfer focus to dropdown
      if (!isUserTyping && !isDropdownFocused) {
        console.log(
          "ProductSearch: Auto-focusing dropdown - user stopped typing, results available"
        );
        setSelectedIndex(0);
        setIsDropdownFocused(true);

        // Remove focus from search input to prevent conflicts
        if (searchInputRef.current) {
          searchInputRef.current.blur();
        }
      }
    } else if (!showDropdown || searchResults.length === 0) {
      // Reset dropdown focus state when dropdown is hidden or no results
      if (isDropdownFocused) {
        console.log(
          "ProductSearch: Returning focus to search input - no results or dropdown hidden"
        );
        setIsDropdownFocused(false);
        setSelectedIndex(-1);

        // Return focus to search input
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }

      // CRITICAL FIX: When no results found, ensure focus stays in search input
      // Apply the same protection logic as "results found" case - only act when user stops typing
      // This prevents focus interference during active typing
      if (searchResults.length === 0 && searchTerm.length >= 3 && !isUserTyping) {
        console.log(
          "ProductSearch: No results found, user stopped typing, ensuring focus stays in search input"
        );
        // Only focus if search input doesn't already have focus (prevent unnecessary focus calls)
        if (searchInputRef.current && document.activeElement !== searchInputRef.current) {
          searchInputRef.current.focus();
        }

        // Ensure dropdown focus state is properly reset for no-results case
        if (isDropdownFocused) {
          setIsDropdownFocused(false);
          setSelectedIndex(-1);
        }
      }
    }
  }, [showDropdown, searchResults.length, isUserTyping, isDropdownFocused, searchTerm]);

  // PROACTIVE FOCUS MANAGEMENT: Ensure focus stays in search input when no results found
  // This runs on search completion, but only when necessary to avoid conflicts
  useEffect(() => {
    // Only act when search has completed (not loading) and no results found
    // AND when the search input doesn't already have focus (to avoid unnecessary focus calls)
    if (
      !isLoading &&
      searchTerm.length >= 3 &&
      searchResults.length === 0 &&
      searchInputRef.current &&
      document.activeElement !== searchInputRef.current
    ) {
      console.log(
        "ProductSearch: Search completed with no results, proactively ensuring focus stays in search input"
      );

      // Use a small delay to avoid conflicts with other focus management
      setTimeout(() => {
        if (searchInputRef.current && document.activeElement !== searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 50);

      // Ensure dropdown state is properly reset
      if (isDropdownFocused) {
        setIsDropdownFocused(false);
        setSelectedIndex(-1);
      }

      // Ensure dropdown is hidden when no results
      if (showDropdown) {
        setShowDropdown(false);
      }
    }
  }, [isLoading, searchTerm, searchResults.length, isDropdownFocused, showDropdown]);

  // Enhanced keyboard handling for precise focus switching
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      const { isDropdownFocused, selectedIndex, showDropdown, searchResults, searchTerm } =
        stateRef.current;

      // IMMEDIATE FOCUS RETURN: Any key pressed while dropdown focused returns to search input
      if (isDropdownFocused) {
        // Check for any printable character, backspace, space, or other input keys
        const isInputKey =
          e.key.length === 1 || // Any single character (letters, numbers, symbols)
          e.key === "Backspace" ||
          e.key === "Delete" ||
          e.key === "Space" ||
          e.key === " " ||
          (e.key.length === 1 && !e.ctrlKey && !e.altKey && !e.metaKey);

        if (isInputKey) {
          e.preventDefault();
          console.log(
            `ProductSearch: Input key "${e.key}" detected while dropdown focused, returning to search input`
          );

          // Immediately return focus to search input
          setIsDropdownFocused(false);
          setSelectedIndex(-1);
          setIsUserTyping(true); // Mark user as actively typing

          if (searchInputRef.current) {
            searchInputRef.current.focus();

            // Process the keystroke based on key type
            if (e.key === "Backspace") {
              // Remove last character
              setSearchTerm((prev) => prev.slice(0, -1));
            } else if (e.key === "Delete") {
              // For delete key, just focus back (cursor position handling)
              // The actual delete will be handled by the input field
            } else if (e.key === "Space" || e.key === " ") {
              // Add space character
              setSearchTerm((prev) => prev + " ");
            } else if (e.key.length === 1) {
              // Add the typed character
              setSearchTerm((prev) => prev + e.key);
            }
          }
          return;
        }
      }

      if (e.key === "Enter") {
        e.preventDefault();

        console.log("=== ENTER KEY PRESSED ===");
        console.log("Search term:", searchTerm);
        console.log("Dropdown focused:", isDropdownFocused);
        console.log("Selected index:", selectedIndex);

        // Check if it's a 13-digit barcode (only when search input is focused)
        if (!isDropdownFocused && searchTerm.length === 13 && /^\d{13}$/.test(searchTerm)) {
          console.log("Detected 13-digit barcode, calling handleBarcodeSearch");
          handleBarcodeSearch(searchTerm);
          return;
        }

        // Handle dropdown selection
        if (showDropdown && searchResults.length > 0 && selectedIndex >= 0) {
          handleProductSelect(searchResults[selectedIndex]);
        } else if (!isDropdownFocused && showDropdown && searchResults.length === 1) {
          // Auto-select single result when Enter pressed from search input
          handleProductSelect(searchResults[0]);
        }
      } else if (e.key === "ArrowDown") {
        e.preventDefault();
        if (showDropdown && searchResults.length > 0) {
          if (!isDropdownFocused) {
            // Transfer focus to dropdown
            setIsDropdownFocused(true);
            setSelectedIndex(0);
            if (searchInputRef.current) {
              searchInputRef.current.blur();
            }
          } else {
            // Navigate within dropdown
            setSelectedIndex((prev) => (prev < searchResults.length - 1 ? prev + 1 : 0));
          }
        }
      } else if (e.key === "ArrowUp") {
        e.preventDefault();
        if (showDropdown && searchResults.length > 0) {
          if (!isDropdownFocused) {
            // Transfer focus to dropdown (last item)
            setIsDropdownFocused(true);
            setSelectedIndex(searchResults.length - 1);
            if (searchInputRef.current) {
              searchInputRef.current.blur();
            }
          } else {
            // Navigate within dropdown
            if (selectedIndex === 0) {
              // Return to search input when going up from first item
              setIsDropdownFocused(false);
              setSelectedIndex(-1);
              if (searchInputRef.current) {
                searchInputRef.current.focus();
              }
            } else {
              setSelectedIndex((prev) => prev - 1);
            }
          }
        }
      } else if (e.key === "Escape") {
        e.preventDefault();
        if (isDropdownFocused) {
          // Return to search input
          setIsDropdownFocused(false);
          setSelectedIndex(-1);
          if (searchInputRef.current) {
            searchInputRef.current.focus();
          }
        } else {
          // Close dropdown entirely
          setShowDropdown(false);
          setSelectedIndex(-1);
          setIsDropdownFocused(false);
        }
      } else if (e.key === "Backspace" && isDropdownFocused && selectedIndex === 0) {
        // Return to search input when backspace at first dropdown item
        e.preventDefault();
        setIsDropdownFocused(false);
        setSelectedIndex(-1);
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }
    },
    [] // No dependencies needed since we use stateRef.current
  );

  // Global keyboard event listener for dropdown navigation
  useEffect(() => {
    if (!isDropdownFocused) return;

    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Convert to React keyboard event format and handle
      const reactEvent = {
        key: e.key,
        preventDefault: () => e.preventDefault(),
        ctrlKey: e.ctrlKey,
        altKey: e.altKey,
        metaKey: e.metaKey,
      } as React.KeyboardEvent;

      handleKeyDown(reactEvent);
    };

    document.addEventListener("keydown", handleGlobalKeyDown);
    return () => document.removeEventListener("keydown", handleGlobalKeyDown);
  }, [isDropdownFocused, handleKeyDown]); // Stable handleKeyDown with no recreations

  // Search products when search term changes (minimum 3 characters)
  useEffect(() => {
    const searchProducts = async () => {
      if (searchTerm.length < 3) {
        setSearchResults([]);
        setShowDropdown(false);
        setIsDropdownFocused(false);
        // Don't set isUserTyping here - let typing timeout handle it
        return;
      }

      // Don't show dropdown for 13-digit barcodes - they should auto-add on Enter
      if (searchTerm.length === 13 && /^\d{13}$/.test(searchTerm)) {
        setSearchResults([]);
        setShowDropdown(false);
        setIsDropdownFocused(false);
        // Don't set isUserTyping here - let typing timeout handle it
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch(
          `/api/products?search=${encodeURIComponent(searchTerm)}&limit=10&includeStock=true`
        );
        if (response.ok) {
          const data = await response.json();
          setSearchResults(data.products || []);
          setShowDropdown(true);
          setSelectedIndex(-1);
          // Don't reset dropdown focus here - let the focus management effect handle it
        }
      } catch (error) {
        console.error("Error searching products:", error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
        // CRITICAL FIX: Don't override isUserTyping here!
        // Let the typing timeout effect be the single source of truth for typing state
        // This prevents focus interference during continuous typing
      }
    };

    const debounceTimer = setTimeout(searchProducts, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchTerm]);

  // Handle typing timeout to determine when user stops typing
  useEffect(() => {
    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // If user is typing, set a timeout to mark them as stopped typing
    if (isUserTyping) {
      typingTimeoutRef.current = setTimeout(() => {
        console.log("ProductSearch: User stopped typing - setting isUserTyping to false");
        setIsUserTyping(false);
      }, 300); // 300ms debounce delay
    }

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [isUserTyping, searchTerm]); // Also depend on searchTerm to reset timeout on each keystroke

  // Handle barcode search
  const handleBarcodeSearch = async (barcode: string) => {
    console.log("=== BARCODE SEARCH ===");
    console.log("Searching for barcode:", barcode);

    setIsLoading(true);
    try {
      // Use the same search endpoint but with the barcode as search term
      // The API will automatically detect exact barcode matches
      const response = await fetch(
        `/api/products?search=${encodeURIComponent(barcode)}&includeStock=true`
      );
      if (response.ok) {
        const data = await response.json();
        console.log("Barcode search response:", data);

        if (data.products && data.products.length > 0) {
          const foundProduct = data.products[0];
          console.log("Found product by barcode:", foundProduct);
          handleProductSelect(foundProduct);
        } else {
          console.log("No product found for barcode:", barcode);
          // Show "Product not found" message
          alert(`Product with barcode ${barcode} not found`);
          clearSearch();
        }
      } else {
        console.error("Barcode search API error:", response.status);
        alert("Error searching for product");
        clearSearch();
      }
    } catch (error) {
      console.error("Error searching by barcode:", error);
      alert("Error searching for product");
      clearSearch();
    } finally {
      setIsLoading(false);
    }
  };

  // Handle product selection
  const handleProductSelect = (product: Product) => {
    console.log("ProductSearch: Product selected, adding to cart");
    addToCart(product);
    clearSearch();
    // Focus management is handled by the main POS page's global focus system
    console.log("ProductSearch: Product selection completed, letting parent handle focus");
  };

  // Clear search and refocus
  const clearSearch = () => {
    setSearchTerm("");
    setSearchResults([]);
    setShowDropdown(false);
    setSelectedIndex(-1);
    setIsDropdownFocused(false);
    setIsUserTyping(false);

    // Clear any pending typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Return focus to search input
    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 100);
  };

  return (
    <div className="relative">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          ref={searchInputRef}
          type="text"
          placeholder="Search products by name, SKU, or scan barcode..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setIsUserTyping(true); // Mark user as actively typing
          }}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            // Return focus to search input and show dropdown if applicable
            setIsDropdownFocused(false);
            setSelectedIndex(-1);

            if (searchTerm.length >= 3 && searchResults.length > 0) {
              setShowDropdown(true);
            }
          }}
          className="pl-10 pr-4 py-3 text-lg"
          disabled={isLoading}
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showDropdown && searchResults.length > 0 && (
        <Card
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-1 max-h-80 overflow-y-auto z-50 shadow-lg"
          data-dropdown
        >
          {searchResults.map((product, index) => (
            <div
              key={product.id}
              className={`p-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors ${
                index === selectedIndex && isDropdownFocused
                  ? "bg-blue-100 border-blue-200 ring-2 ring-blue-300 ring-opacity-50" // Focused state
                  : index === selectedIndex
                    ? "bg-blue-50" // Selected but not focused
                    : "hover:bg-gray-50" // Hover state
              }`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleProductSelect(product);
              }}
              onMouseDown={(e) => {
                e.preventDefault(); // Prevent focus loss from search input
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Package className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium text-gray-900">{product.name}</div>
                    <div className="text-sm text-gray-500">
                      SKU: {product.sku} | Stock: {product.storeStock?.quantity || 0}{" "}
                      {product.unit.abbreviation}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-gray-900">
                    Rp {Number(product.basePrice).toLocaleString("id-ID")}
                  </div>
                  <div className="text-sm text-gray-500">per {product.unit.abbreviation}</div>
                </div>
              </div>
            </div>
          ))}
        </Card>
      )}

      {/* No Products Found Message */}
      {!isLoading && searchTerm.length >= 3 && searchResults.length === 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 z-40">
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4 text-center">
            <div className="text-gray-500 text-sm">
              No products found. Try a different search term.
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
