"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { POStatus, POStatusChangeReason, POPriority } from "@prisma/client";
import { getValidTransitions, PO_STATUS_CHANGE_REASONS } from "@/lib/po-status-management";
import { toast } from "sonner";
import { Loader2, ArrowRight } from "lucide-react";

interface POStatusTransitionProps {
  purchaseOrderId: string;
  currentStatus: POStatus;
  userRole: string;
  onStatusChanged?: (newStatus: POStatus) => void;
  trigger?: React.ReactNode;
}

export function POStatusTransition({
  purchaseOrderId,
  currentStatus,
  userRole,
  onStatusChanged,
  trigger,
}: POStatusTransitionProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<POStatus | "">("");
  const [reason, setReason] = useState<POStatusChangeReason | "">("");
  const [notes, setNotes] = useState("");
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState("");
  const [holdReason, setHoldReason] = useState("");
  const [holdUntil, setHoldUntil] = useState("");

  const validTransitions = getValidTransitions(currentStatus, userRole);

  const handleSubmit = async () => {
    if (!selectedStatus || !reason) {
      toast.error("Please select a status and reason");
      return;
    }

    try {
      setLoading(true);

      const requestData: any = {
        toStatus: selectedStatus,
        reason,
        notes: notes || undefined,
      };

      if (expectedDeliveryDate) {
        requestData.expectedDeliveryDate = new Date(expectedDeliveryDate).toISOString();
      }

      if (selectedStatus === 'ON_HOLD') {
        requestData.holdReason = holdReason || undefined;
        if (holdUntil) {
          requestData.holdUntil = new Date(holdUntil).toISOString();
        }
      }

      const response = await fetch(`/api/purchase-orders/${purchaseOrderId}/status-transition`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to change status");
      }

      const updatedPO = await response.json();
      
      toast.success(`Status changed to ${selectedStatus}`);
      onStatusChanged?.(selectedStatus);
      setOpen(false);
      
      // Reset form
      setSelectedStatus("");
      setReason("");
      setNotes("");
      setExpectedDeliveryDate("");
      setHoldReason("");
      setHoldUntil("");
    } catch (error) {
      console.error("Error changing status:", error);
      toast.error(error instanceof Error ? error.message : "Failed to change status");
    } finally {
      setLoading(false);
    }
  };

  const selectedTransition = validTransitions.find(t => t.to === selectedStatus);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <ArrowRight className="h-4 w-4 mr-2" />
            Change Status
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Change Purchase Order Status</DialogTitle>
          <DialogDescription>
            Change the status of this purchase order. Current status: <strong>{currentStatus}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="status">New Status</Label>
            <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as POStatus)}>
              <SelectTrigger>
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {validTransitions.map((transition) => (
                  <SelectItem key={transition.to} value={transition.to}>
                    <div className="flex flex-col">
                      <span>{transition.label}</span>
                      <span className="text-xs text-muted-foreground">
                        {transition.description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason</Label>
            <Select value={reason} onValueChange={(value) => setReason(value as POStatusChangeReason)}>
              <SelectTrigger>
                <SelectValue placeholder="Select reason for change" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(PO_STATUS_CHANGE_REASONS).map(([key, label]) => (
                  <SelectItem key={key} value={key}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedStatus === 'ON_HOLD' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="holdReason">Hold Reason</Label>
                <Input
                  id="holdReason"
                  value={holdReason}
                  onChange={(e) => setHoldReason(e.target.value)}
                  placeholder="Reason for putting on hold"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="holdUntil">Hold Until (Optional)</Label>
                <Input
                  id="holdUntil"
                  type="datetime-local"
                  value={holdUntil}
                  onChange={(e) => setHoldUntil(e.target.value)}
                />
              </div>
            </>
          )}

          {(selectedStatus === 'ORDERED' || selectedStatus === 'EXPEDITED') && (
            <div className="space-y-2">
              <Label htmlFor="expectedDeliveryDate">Expected Delivery Date (Optional)</Label>
              <Input
                id="expectedDeliveryDate"
                type="datetime-local"
                value={expectedDeliveryDate}
                onChange={(e) => setExpectedDeliveryDate(e.target.value)}
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes about this status change"
              rows={3}
            />
          </div>

          {selectedTransition && (
            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>Action:</strong> {selectedTransition.description}
              </p>
              {selectedTransition.requiresApproval && (
                <p className="text-sm text-orange-600 mt-1">
                  ⚠️ This action requires approval
                </p>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={loading || !selectedStatus || !reason}
          >
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Change Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
