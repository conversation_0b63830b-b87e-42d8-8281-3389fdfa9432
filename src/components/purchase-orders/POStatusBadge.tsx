"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { POStatus, POPriority } from "@prisma/client";
import { getStatusInfo, PO_PRIORITY_INFO } from "@/lib/po-status-management";
import { 
  FileEdit, 
  Clock, 
  CheckCircle, 
  Send, 
  PackageOpen, 
  PackageCheck, 
  XCircle, 
  AlertTriangle,
  Pause,
  Zap,
  LucideIcon
} from "lucide-react";

const statusIcons: Record<POStatus, LucideIcon> = {
  DRAFT: FileEdit,
  PENDING_APPROVAL: Clock,
  APPROVED: CheckCircle,
  ORDERED: Send,
  PARTIALLY_RECEIVED: PackageOpen,
  RECEIVED: PackageCheck,
  CANCELLED: XCircle,
  OVERDUE: AlertTriangle,
  ON_HOLD: Pause,
  EXPEDITED: Zap,
};

interface POStatusBadgeProps {
  status: POStatus;
  priority?: POPriority;
  showProgress?: boolean;
  showIcon?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function POStatusBadge({
  status,
  priority,
  showProgress = false,
  showIcon = true,
  size = "md",
  className = "",
}: POStatusBadgeProps) {
  const statusInfo = getStatusInfo(status);
  const Icon = statusIcons[status];
  
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-2.5 py-1.5",
    lg: "text-base px-3 py-2",
  };

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  return (
    <TooltipProvider>
      <div className={`flex items-center gap-2 ${className}`}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              className={`
                ${statusInfo.bgColor} ${statusInfo.color} 
                ${sizeClasses[size]}
                flex items-center gap-1.5
                border-0 font-medium
              `}
            >
              {showIcon && <Icon className={iconSizes[size]} />}
              {statusInfo.label}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p className="font-medium">{statusInfo.label}</p>
              <p className="text-sm text-muted-foreground">{statusInfo.description}</p>
              {showProgress && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>Progress</span>
                    <span>{statusInfo.progress}%</span>
                  </div>
                  <Progress value={statusInfo.progress} className="h-1" />
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>

        {priority && priority !== 'NORMAL' && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge 
                className={`
                  ${PO_PRIORITY_INFO[priority].bgColor} ${PO_PRIORITY_INFO[priority].color}
                  ${sizeClasses[size]}
                  border-0 font-medium
                `}
              >
                {PO_PRIORITY_INFO[priority].label}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>Priority: {PO_PRIORITY_INFO[priority].label}</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}

interface POStatusProgressProps {
  status: POStatus;
  className?: string;
}

export function POStatusProgress({ status, className = "" }: POStatusProgressProps) {
  const statusInfo = getStatusInfo(status);
  
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex justify-between text-sm">
        <span className="font-medium">{statusInfo.label}</span>
        <span className="text-muted-foreground">{statusInfo.progress}%</span>
      </div>
      <Progress 
        value={statusInfo.progress} 
        className="h-2"
      />
      <p className="text-xs text-muted-foreground">{statusInfo.description}</p>
    </div>
  );
}

interface POStatusTimelineProps {
  currentStatus: POStatus;
  className?: string;
}

export function POStatusTimeline({ currentStatus, className = "" }: POStatusTimelineProps) {
  const statuses: POStatus[] = [
    'DRAFT',
    'PENDING_APPROVAL', 
    'APPROVED',
    'ORDERED',
    'PARTIALLY_RECEIVED',
    'RECEIVED'
  ];

  const currentIndex = statuses.indexOf(currentStatus);
  
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {statuses.map((status, index) => {
        const statusInfo = getStatusInfo(status);
        const Icon = statusIcons[status];
        const isActive = index <= currentIndex;
        const isCurrent = status === currentStatus;
        
        return (
          <div key={status} className="flex items-center">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className={`
                      flex items-center justify-center w-8 h-8 rounded-full border-2
                      ${isActive 
                        ? `${statusInfo.bgColor} ${statusInfo.color} border-current` 
                        : 'bg-gray-100 text-gray-400 border-gray-300'
                      }
                      ${isCurrent ? 'ring-2 ring-offset-2 ring-blue-500' : ''}
                    `}
                  >
                    <Icon className="h-4 w-4" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{statusInfo.label}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {index < statuses.length - 1 && (
              <div 
                className={`
                  w-8 h-0.5 mx-1
                  ${index < currentIndex ? 'bg-blue-500' : 'bg-gray-300'}
                `}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}
