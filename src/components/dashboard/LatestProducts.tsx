"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/custom/badge";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Package, AlertCircle, ExternalLink } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useClientAuth } from "@/hooks/use-client-auth";
import { formatCurrency } from "@/lib/utils";
import { Receipt, ArrowRight } from "lucide-react";
import Link from "next/link";

// Define product type
type Product = {
  id: string;
  name: string;
  sku: string;
  basePrice: number;
  imageUrl: string | null;
  createdAt: string;
  category: {
    id: string;
    name: string;
  } | null;
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
  supplier: {
    id: string;
    name: string;
  } | null;
  storeStock: {
    quantity: number;
  } | null;
};

export function LatestProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useClientAuth();

  // Fetch latest products
  useEffect(() => {
    const fetchLatestProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch("/api/products/latest");

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch latest products");
        }

        const data = await response.json();
        setProducts(data.products);
      } catch (err: any) {
        setError(err.message || "An error occurred while fetching products");
        console.error("Error fetching latest products:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchLatestProducts();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-3 border-b">
        <CardTitle className="text-base font-medium">Latest Products</CardTitle>
        <Button variant="ghost" size="sm" className="gap-1" asChild>
          <Link href="inventory/products">
            View All
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="pt-4">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center py-4">
            <div className="text-center text-sm text-muted-foreground">Loading products...</div>
          </div>
        ) : products.length === 0 ? (
          <div className="flex justify-center py-4">
            <div className="text-center text-sm text-muted-foreground">No products found</div>
          </div>
        ) : (
          <div className="space-y-4">
            {products.map((product) => (
              <div key={product.id} className="flex items-start gap-3 border-b pb-3 last:border-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                  <Package className="h-4 w-4 text-primary" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <Link
                      href={`/inventory/products/${product.id}`}
                      className="font-medium hover:underline flex items-center"
                    >
                      {product.name}
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </Link>
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(product.createdAt), "MMM d, HH:mm")}
                    </div>
                  </div>
                  <div className="mt-1 flex items-center gap-2">
                    <Badge variant="outline">{product.category?.name || "No Category"}</Badge>
                    <div className="text-xs text-muted-foreground">{product.sku}</div>
                  </div>
                  <div className="mt-1 flex items-center justify-between">
                    <div className="text-xs font-medium">{formatCurrency(product.basePrice)}</div>
                    <div className="text-xs text-muted-foreground">
                      Stock: {product.storeStock?.quantity || 0} {product.unit.abbreviation}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
