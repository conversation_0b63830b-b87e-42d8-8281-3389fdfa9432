"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export function ActiveUsersCard() {
  const [userCount, setUserCount] = useState<{ total: number; active: number } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch user count
  useEffect(() => {
    const fetchUserCount = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch("/api/users/count");

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch user count");
        }

        const data = await response.json();
        setUserCount(data);
      } catch (err: any) {
        setError(err.message || "An error occurred while fetching user count");
        console.error("Error fetching user count:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchUserCount();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Active Users</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {error ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : loading ? (
          <div className="text-sm text-muted-foreground">Loading...</div>
        ) : (
          <>
            <div className="text-2xl font-bold">{userCount?.active || 0}</div>
            <div className="flex items-center pt-1 text-xs text-muted-foreground">
              <span>Out of {userCount?.total || 0} total users</span>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
