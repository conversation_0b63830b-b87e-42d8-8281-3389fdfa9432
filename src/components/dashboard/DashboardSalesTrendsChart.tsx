"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { formatCurrency, formatChartDate, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { SalesTrendData } from "@/lib/types/analytics";
import { subDays } from "date-fns";

interface DashboardSalesTrendsChartProps {
  className?: string;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">{formatChartDate(label, 'medium')}</p>
        <div className="mt-2 space-y-1">
          <div className="flex items-center justify-between gap-4">
            <span className="text-sm text-gray-600">Revenue:</span>
            <span className="font-medium text-blue-600">{formatCurrency(data.revenue)}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-sm text-gray-600">Transactions:</span>
            <span className="font-medium text-gray-900">{data.transactions}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-sm text-gray-600">Avg Order:</span>
            <span className="font-medium text-green-600">{formatCurrency(data.averageOrderValue)}</span>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

export function DashboardSalesTrendsChart({ className }: DashboardSalesTrendsChartProps) {
  const [data, setData] = useState<SalesTrendData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Set default date range for dashboard (last 7 days)
      const fromDate = subDays(new Date(), 7);
      const toDate = new Date();

      const params = new URLSearchParams();
      params.append("fromDate", fromDate.toISOString());
      params.append("toDate", toDate.toISOString());
      params.append("dateRange", "7d");

      console.log("[DashboardSalesTrendsChart] Fetching with params:", params.toString());

      const response = await fetch(`/api/analytics/sales-trends?${params.toString()}`);

      console.log("Dashboard sales trends response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Dashboard sales trends API error:", errorText);
        throw new Error(`Failed to fetch sales trends: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      console.log("Dashboard sales trends result success:", result.success);

      if (!result.success) {
        console.error("Dashboard sales trends result error:", result.error);
        throw new Error(result.error || "Failed to fetch sales trends");
      }

      setData(result.data);
    } catch (err) {
      console.error("Error fetching dashboard sales trends:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleRefresh = () => {
    fetchData();
  };

  return (
    <BaseChart
      title="Sales Trends"
      subtitle="Revenue patterns over the last 7 days"
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={200}>
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="dashboardRevenueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={CHART_COLORS.primary[0]} stopOpacity={0.8} />
              <stop offset="95%" stopColor={CHART_COLORS.primary[0]} stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => formatChartDate(value)}
            stroke="#666"
            fontSize={12}
          />
          <YAxis tickFormatter={(value) => formatCurrency(value)} stroke="#666" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="revenue"
            stroke={CHART_COLORS.primary[0]}
            strokeWidth={2}
            fill="url(#dashboardRevenueGradient)"
            dot={{ fill: CHART_COLORS.primary[0], strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: CHART_COLORS.primary[0], strokeWidth: 2 }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
