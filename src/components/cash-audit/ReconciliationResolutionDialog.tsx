"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Loader2, AlertTriangle, CheckCircle, Clock, ArrowUp } from "lucide-react";

interface ReconciliationResolutionDialogProps {
  reconciliation: {
    id: string;
    businessDate: string;
    cashier: string;
    discrepancy: number;
    discrepancyCategory?: string;
    resolutionStatus?: string;
    resolutionNotes?: string;
    notes?: string;
  } | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onResolved: () => void;
}

const RESOLUTION_STATUSES = [
  { value: "INVESTIGATING", label: "Under Investigation", icon: Clock, color: "bg-yellow-500" },
  { value: "RESOLVED", label: "Resolved", icon: CheckCircle, color: "bg-green-500" },
  { value: "WRITTEN_OFF", label: "Written Off", icon: AlertTriangle, color: "bg-orange-500" },
  { value: "ESCALATED", label: "Escalated", icon: ArrowUp, color: "bg-red-500" },
];

const DISCREPANCY_CATEGORIES = [
  { value: "COUNTING_ERROR", label: "Counting Error" },
  { value: "SYSTEM_ERROR", label: "System Error" },
  { value: "THEFT_SUSPECTED", label: "Theft Suspected" },
  { value: "CASH_SHORTAGE", label: "Cash Shortage" },
  { value: "CASH_SURPLUS", label: "Cash Surplus" },
  { value: "REGISTER_ERROR", label: "Register Error" },
  { value: "TRAINING_ERROR", label: "Training Error" },
  { value: "PROCEDURAL_ERROR", label: "Procedural Error" },
  { value: "UNKNOWN", label: "Unknown" },
  { value: "OTHER", label: "Other" },
];

export function ReconciliationResolutionDialog({
  reconciliation,
  open,
  onOpenChange,
  onResolved,
}: ReconciliationResolutionDialogProps) {
  const [loading, setLoading] = useState(false);
  const [resolutionStatus, setResolutionStatus] = useState("");
  const [resolutionNotes, setResolutionNotes] = useState("");
  const [discrepancyCategory, setDiscrepancyCategory] = useState("");

  // Reset form when dialog opens
  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen && reconciliation) {
      setResolutionStatus(reconciliation.resolutionStatus || "");
      setResolutionNotes(reconciliation.resolutionNotes || "");
      setDiscrepancyCategory(reconciliation.discrepancyCategory || "");
    } else {
      setResolutionStatus("");
      setResolutionNotes("");
      setDiscrepancyCategory("");
    }
    onOpenChange(newOpen);
  };

  const handleSubmit = async () => {
    if (!reconciliation) return;

    if (!resolutionStatus) {
      toast.error("Please select a resolution status");
      return;
    }

    if (!resolutionNotes.trim()) {
      toast.error("Please provide resolution notes");
      return;
    }

    try {
      setLoading(true);

      const response = await fetch(`/api/cash-reconciliation/${reconciliation.id}/resolve`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          resolutionStatus,
          resolutionNotes: resolutionNotes.trim(),
          discrepancyCategory: discrepancyCategory || undefined,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to resolve reconciliation");
      }

      toast.success("Reconciliation updated successfully");
      onResolved();
      onOpenChange(false);
    } catch (error: any) {
      console.error("Error resolving reconciliation:", error);
      toast.error(error.message || "Failed to resolve reconciliation");
    } finally {
      setLoading(false);
    }
  };

  if (!reconciliation) return null;

  const discrepancyAmount = Math.abs(reconciliation.discrepancy);
  const isShortage = reconciliation.discrepancy < 0;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Resolve Cash Reconciliation</DialogTitle>
          <DialogDescription>
            Update the resolution status and provide notes for this cash discrepancy.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Reconciliation Details */}
          <div className="bg-muted/50 p-4 rounded-lg space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">Business Date:</span>
              <span>{new Date(reconciliation.businessDate).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Cashier:</span>
              <span>{reconciliation.cashier}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Discrepancy:</span>
              <div className="flex items-center gap-2">
                <Badge variant={isShortage ? "destructive" : "default"}>
                  {isShortage ? "-" : "+"}Rp {discrepancyAmount.toLocaleString("id-ID")}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  ({isShortage ? "Shortage" : "Surplus"})
                </span>
              </div>
            </div>
            {reconciliation.notes && (
              <div className="space-y-1">
                <span className="font-medium">Original Transaction Notes:</span>
                <p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded">
                  {reconciliation.notes}
                </p>
              </div>
            )}
            {reconciliation.resolutionNotes && (
              <div className="space-y-1">
                <span className="font-medium">Previous Resolution Notes:</span>
                <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded border border-blue-200">
                  {reconciliation.resolutionNotes}
                </p>
              </div>
            )}
          </div>

          {/* Resolution Form */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="resolutionStatus">Resolution Status *</Label>
              <Select value={resolutionStatus} onValueChange={setResolutionStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select resolution status" />
                </SelectTrigger>
                <SelectContent>
                  {RESOLUTION_STATUSES.map((status) => {
                    const Icon = status.icon;
                    return (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          {status.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="discrepancyCategory">Discrepancy Category</Label>
              <Select value={discrepancyCategory} onValueChange={setDiscrepancyCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category (optional)" />
                </SelectTrigger>
                <SelectContent>
                  {DISCREPANCY_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="resolutionNotes">
                {reconciliation.resolutionNotes
                  ? "Update Resolution Notes *"
                  : "Resolution Notes *"}
              </Label>
              <Textarea
                id="resolutionNotes"
                placeholder={
                  reconciliation.resolutionNotes
                    ? "Update or add to the existing resolution notes..."
                    : "Provide detailed notes about the resolution..."
                }
                value={resolutionNotes}
                onChange={(e) => setResolutionNotes(e.target.value)}
                rows={4}
              />
              <p className="text-xs text-muted-foreground">
                {reconciliation.resolutionNotes
                  ? "These notes will replace the previous resolution notes shown above."
                  : "Explain what caused the discrepancy and how it was resolved."}
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Update Resolution
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
