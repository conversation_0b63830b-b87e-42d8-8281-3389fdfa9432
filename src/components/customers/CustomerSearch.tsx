"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Search, X } from "lucide-react";

export interface CustomerSearchFilters {
  search: string;
  customerType: string;
}

interface CustomerSearchProps {
  onFilterChange: (filters: CustomerSearchFilters) => void;
}

export function CustomerSearch({ onFilterChange }: CustomerSearchProps) {
  const [filters, setFilters] = useState<CustomerSearchFilters>({
    search: "",
    customerType: "ALL",
  });

  const [searchInput, setSearchInput] = useState("");

  // Apply filters when search button is clicked
  const applyFilters = () => {
    setFilters((prev) => ({ ...prev, search: searchInput }));
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchInput("");
    setFilters({
      search: "",
      customerType: "ALL",
    });
  };

  // Update customer type filter
  const handleCustomerTypeChange = (value: string) => {
    setFilters((prev) => ({ ...prev, customerType: value }));
  };

  // Notify parent component when filters change
  useEffect(() => {
    onFilterChange(filters);
  }, [filters]);

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <Input
              placeholder="Search by name, phone, email..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  applyFilters();
                }
              }}
            />
            <Button variant="outline" onClick={applyFilters} className="shrink-0">
              <Search className="h-4 w-4" />
            </Button>
          </div>

          <div>
            <Select value={filters.customerType} onValueChange={handleCustomerTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Customer Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Types</SelectItem>
                <SelectItem value="REGULAR">Regular</SelectItem>
                <SelectItem value="FRIEND">Friend</SelectItem>
                <SelectItem value="FAMILY">Family</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end">
            <Button variant="ghost" onClick={clearFilters} className="h-10">
              <X className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
