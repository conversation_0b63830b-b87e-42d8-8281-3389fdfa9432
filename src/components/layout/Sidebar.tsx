"use client";

import * as React from "react";
import { useState, useEffect, createContext, useContext } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useClientAuth } from "@/hooks/use-client-auth";
import { useSettings } from "@/contexts/settings-context";
import { useStoreInfo } from "@/contexts/store-info-context";
import {
  Settings,
  HelpCircle,
  ChevronDown,
  Database,
  UserPlus,
  ClipboardList,
  LayoutDashboard,
  MessageSquare,
  Package,
  ShoppingCart,
  Tags,
  Ruler,
  Truck,
  Beaker,
  Boxes,
  Receipt,
  Users,
  DollarSign,
  AlertTriangle,
  Map,
  Monitor,
  Calculator,
  BarChart3,
  Target,
  RotateCcw,
  FileText,
  Bell,
} from "lucide-react";

// Create a context for the sidebar
interface SidebarContextType {
  onClose?: () => void;
}

const SidebarContext = createContext<SidebarContextType>({});

interface SidebarProps {
  className?: string;
  onClose?: () => void;
}

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  badge?: string | number;
}

interface NavGroupProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  maxVisibleItems?: number;
}

const NavItem = ({ href, icon, label, active, badge }: NavItemProps) => {
  const { onClose } = useContext(SidebarContext);

  const handleClick = () => {
    if (window.innerWidth < 1024 && onClose) {
      onClose();
    }
  };

  return (
    <Link
      href={href}
      className={cn(
        "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
        active
          ? "bg-accent text-accent-foreground font-medium"
          : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"
      )}
      onClick={handleClick}
    >
      {icon}
      <span className="flex-1">{label}</span>
      {badge && (
        <span className="flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary">
          {badge}
        </span>
      )}
    </Link>
  );
};

const NavGroup = ({
  title,
  children,
  defaultExpanded = true,
  maxVisibleItems = 3,
}: NavGroupProps) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [showAll, setShowAll] = useState(false);

  // Convert children to array for manipulation
  const childrenArray = React.Children.toArray(children).filter(Boolean);
  const hasMoreItems = childrenArray.length > maxVisibleItems;
  const visibleItems = showAll ? childrenArray : childrenArray.slice(0, maxVisibleItems);
  const hiddenCount = childrenArray.length - maxVisibleItems;

  // Load saved state from localStorage
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(`sidebar-group-${title}`);
      if (savedState !== null) {
        const state = JSON.parse(savedState);
        if (typeof state.isExpanded === "boolean") {
          setIsExpanded(state.isExpanded);
        }
        if (typeof state.showAll === "boolean") {
          setShowAll(state.showAll);
        }
      }
    } catch (error) {
      console.warn("Failed to load sidebar state from localStorage:", error);
    }
  }, [title]);

  // Save state to localStorage
  const saveState = (expanded: boolean, showAllItems: boolean) => {
    try {
      localStorage.setItem(
        `sidebar-group-${title}`,
        JSON.stringify({
          isExpanded: expanded,
          showAll: showAllItems,
        })
      );
    } catch (error) {
      console.warn("Failed to save sidebar state to localStorage:", error);
    }
  };

  const toggleExpanded = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    if (!newExpanded) {
      setShowAll(false);
      saveState(newExpanded, false);
    } else {
      saveState(newExpanded, showAll);
    }
  };

  const toggleShowAll = () => {
    const newShowAll = !showAll;
    setShowAll(newShowAll);
    saveState(isExpanded, newShowAll);
  };

  return (
    <div className="py-2">
      <button
        onClick={toggleExpanded}
        className="flex w-full items-center justify-between px-3 py-1 text-xs uppercase tracking-wider text-muted-foreground hover:text-foreground transition-colors"
        aria-expanded={isExpanded}
        aria-label={`${isExpanded ? "Collapse" : "Expand"} ${title} section`}
      >
        <span>{title}</span>
        <ChevronDown
          className={cn(
            "h-3 w-3 transition-transform duration-200",
            isExpanded ? "rotate-0" : "-rotate-90"
          )}
        />
      </button>

      <div
        className={cn(
          "overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
        )}
      >
        <div className="space-y-1 pt-1">
          {visibleItems}
          {hasMoreItems && isExpanded && (
            <button
              onClick={toggleShowAll}
              className="flex w-full items-center gap-2 rounded-md px-3 py-2 text-xs text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground transition-colors"
              aria-expanded={showAll}
              aria-label={
                showAll ? `Show fewer ${title} items` : `Show ${hiddenCount} more ${title} items`
              }
            >
              <ChevronDown
                className={cn(
                  "h-3 w-3 transition-transform duration-200",
                  showAll ? "rotate-180" : "rotate-0"
                )}
              />
              <span>{showAll ? "Show Less" : `Show ${hiddenCount} More`}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export function Sidebar({ className, onClose }: SidebarProps) {
  const pathname = usePathname();
  const { user } = useClientAuth();
  const { isChatEnabled } = useSettings();
  const { storeInfo, isLoading: storeLoading } = useStoreInfo();
  const isDeveloper = user?.role === "DEVELOPER";
  const isSuperAdmin = user?.role === "SUPER_ADMIN";
  const isAdmin = isDeveloper || isSuperAdmin || user?.role === "FINANCE_ADMIN";
  const isCashier = user?.role === "CASHIER";

  // Create context value
  const contextValue = {
    onClose,
  };

  return (
    <SidebarContext.Provider value={contextValue}>
      <div className={cn("flex h-screen flex-col border-r bg-card px-3 py-4 w-[240px]", className)}>
        <div className="flex items-center px-2">
          <Link href="/" className="flex items-center gap-2 font-semibold text-primary">
            <span className="flex h-8 w-8 items-center justify-center rounded bg-primary/10 text-primary">
              {storeLoading ? (
                <div className="h-4 w-4 bg-primary/20 animate-pulse rounded"></div>
              ) : storeInfo?.storeName ? (
                storeInfo.storeName.charAt(0).toUpperCase()
              ) : (
                "N"
              )}
            </span>
            {storeLoading ? (
              <div className="h-5 w-20 bg-gray-200 animate-pulse rounded"></div>
            ) : (
              <span>{storeInfo?.storeName || "Next POS"}</span>
            )}
          </Link>
        </div>

        <div className="mt-6 flex flex-1 flex-col overflow-y-auto">
          <NavGroup title="MAIN" defaultExpanded={true} maxVisibleItems={4}>
            {!isCashier && (
              <NavItem
                href="/dashboard"
                icon={<LayoutDashboard className="h-4 w-4" />}
                label="Dashboard"
                active={pathname === "/dashboard"}
              />
            )}
            {isCashier && (
              <NavItem
                href="/pos"
                icon={<ShoppingCart className="h-4 w-4" />}
                label="Point of Sale"
                active={pathname === "/pos"}
              />
            )}
            <NavItem
              href="/transactions"
              icon={<Receipt className="h-4 w-4" />}
              label="Transactions"
              active={pathname.startsWith("/transactions")}
            />
            <NavItem
              href="/customers"
              icon={<Users className="h-4 w-4" />}
              label="Customers"
              active={pathname.startsWith("/customers")}
            />
            <NavItem
              href="/notifications"
              icon={<Bell className="h-4 w-4" />}
              label="Notifications"
              active={pathname.startsWith("/notifications")}
            />
          </NavGroup>

          {/* Only show inventory section for non-developer and non-cashier roles */}
          {!isDeveloper && !isCashier && (
            <NavGroup title="INVENTORY" defaultExpanded={true} maxVisibleItems={3}>
              <NavItem
                href="/inventory/products"
                icon={<Package className="h-4 w-4" />}
                label="Products"
                active={pathname.startsWith("/inventory/products")}
              />
              <NavItem
                href="/inventory/categories"
                icon={<Tags className="h-4 w-4" />}
                label="Categories"
                active={pathname.startsWith("/inventory/categories")}
              />
              <NavItem
                href="/inventory/units"
                icon={<Ruler className="h-4 w-4" />}
                label="Units"
                active={pathname.startsWith("/inventory/units")}
              />
              <NavItem
                href="/inventory/suppliers"
                icon={<Truck className="h-4 w-4" />}
                label="Suppliers"
                active={pathname.startsWith("/inventory/suppliers")}
              />
              <NavItem
                href="/inventory/purchase-orders"
                icon={<FileText className="h-4 w-4" />}
                label="Purchase Orders"
                active={
                  pathname.startsWith("/inventory/purchase-orders") &&
                  !pathname.includes("/templates") &&
                  !pathname.includes("/reports")
                }
              />
              <NavItem
                href="/inventory/purchase-order-templates"
                icon={<FileText className="h-4 w-4" />}
                label="PO Templates"
                active={pathname.startsWith("/inventory/purchase-order-templates")}
              />
              <NavItem
                href="/inventory/purchase-orders/reports"
                icon={<ClipboardList className="h-4 w-4" />}
                label="PO Reports"
                active={pathname.startsWith("/inventory/purchase-orders/reports")}
              />
              <NavItem
                href="/inventory/stock"
                icon={<Boxes className="h-4 w-4" />}
                label="Stock Management"
                active={
                  pathname === "/inventory/stock" ||
                  pathname.startsWith("/inventory/stock/edit") ||
                  pathname === "/inventory/stock/new" ||
                  pathname === "/inventory/stock/adjustments" ||
                  pathname === "/inventory/stock/simple-transfers"
                }
              />
              <NavItem
                href="/inventory/batches"
                icon={<Package className="h-4 w-4" />}
                label="Batch Tracking"
                active={pathname.startsWith("/inventory/batches")}
              />
              <NavItem
                href="/inventory/returns"
                icon={<RotateCcw className="h-4 w-4" />}
                label="Returns & Exchanges"
                active={pathname.startsWith("/inventory/returns")}
              />
              <NavItem
                href="/inventory/reports"
                icon={<ClipboardList className="h-4 w-4" />}
                label="Inventory Reports"
                active={pathname.startsWith("/inventory/reports")}
              />
            </NavGroup>
          )}

          {/* Finance menu for super admin and finance admin */}
          {!isDeveloper && (isSuperAdmin || user?.role === "FINANCE_ADMIN") && (
            <NavGroup title="FINANCE" defaultExpanded={true} maxVisibleItems={3}>
              {/* Revenue Targets for super admin and finance admin */}
              <NavItem
                href="/admin/revenue-targets"
                icon={<Target className="h-4 w-4" />}
                label="Revenue Targets"
                active={pathname.startsWith("/admin/revenue-targets")}
              />

              {/* Cash Audit for super admin and finance admin */}
              <NavItem
                href="/admin/cash-audit"
                icon={<Calculator className="h-4 w-4" />}
                label="Cash Audit"
                active={pathname.startsWith("/admin/cash-audit")}
              />

              {/* Analytics for super admin and finance admin */}
              <NavItem
                href="/admin/analytics"
                icon={<BarChart3 className="h-4 w-4" />}
                label="Analytics"
                active={pathname.startsWith("/admin/analytics")}
              />

              {/* Cash Drawers for super admin and finance admin */}
              <NavItem
                href="/admin/cash-drawers"
                icon={<DollarSign className="h-4 w-4" />}
                label="Cash Drawers"
                active={pathname.startsWith("/admin/cash-drawers")}
              />
            </NavGroup>
          )}

          {/* Admin menu for super admin and finance admin, but not for developer */}
          {!isDeveloper && (isSuperAdmin || user?.role === "FINANCE_ADMIN") && (
            <NavGroup title="ADMIN" defaultExpanded={true} maxVisibleItems={3}>
              {/* User Management is only for super admin */}
              {isSuperAdmin && (
                <NavItem
                  href="/admin/users"
                  icon={<UserPlus className="h-4 w-4" />}
                  label="User Management"
                  active={pathname === "/admin/users"}
                />
              )}

              {/* Activity Logs for super admin and finance admin */}
              <NavItem
                href="/admin/activity-logs"
                icon={<ClipboardList className="h-4 w-4" />}
                label="Activity Logs"
                active={pathname === "/admin/activity-logs"}
              />

              {/* Terminals for super admin and finance admin */}
              <NavItem
                href="/admin/terminals"
                icon={<Monitor className="h-4 w-4" />}
                label="Terminals"
                active={pathname.startsWith("/admin/terminals")}
              />

              {/* Backup & Restore for super admin and finance admin */}
              <NavItem
                href="/admin/backup"
                icon={<Database className="h-4 w-4" />}
                label="Backup & Restore"
                active={pathname === "/admin/backup"}
              />

              {/* Chat Management is only for super admin and only if chat is enabled */}
              {isSuperAdmin && isChatEnabled && (
                <NavItem
                  href="/admin/chat"
                  icon={<MessageSquare className="h-4 w-4" />}
                  label="Chat Management"
                  active={pathname === "/admin/chat"}
                />
              )}
            </NavGroup>
          )}

          {/* Always show development section for developer role */}
          {isDeveloper && (
            <NavGroup title="DEVELOPMENT" defaultExpanded={true} maxVisibleItems={3}>
              <NavItem
                href="/tests"
                icon={<Beaker className="h-4 w-4" />}
                label="API Tests"
                active={pathname.startsWith("/tests")}
              />
            </NavGroup>
          )}

          {!isCashier && (
            <NavGroup title="SETTINGS" defaultExpanded={true} maxVisibleItems={3}>
              <NavItem
                href="/settings"
                icon={<Settings className="h-4 w-4" />}
                label="Settings"
                active={pathname === "/settings"}
              />
              <NavItem
                href="/help"
                icon={<HelpCircle className="h-4 w-4" />}
                label="Help"
                active={pathname === "/help"}
              />
            </NavGroup>
          )}
        </div>

        <div className="mt-auto border-t pt-4">
          <div className="flex items-center gap-3 rounded-md px-3 py-2">
            <div className="flex flex-col"></div>
          </div>
          <div className="mt-2 px-3"></div>
        </div>
      </div>
    </SidebarContext.Provider>
  );
}
