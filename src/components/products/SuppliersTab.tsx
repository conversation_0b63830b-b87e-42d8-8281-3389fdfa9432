"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { formatCurrency } from "@/lib/utils";
import {
  Plus,
  Edit,
  Trash2,
  Star,
  StarOff,
  Loader2,
  Phone,
  Mail,
  User,
  Package,
  Clock,
  DollarSign,
} from "lucide-react";
import { AddSupplierDialog } from "./AddSupplierDialog";
import { EditSupplierDialog } from "./EditSupplierDialog";
import { toast } from "sonner";

interface ProductSupplier {
  id: string;
  supplierId: string;
  supplierProductCode?: string | null;
  supplierProductName?: string | null;
  purchasePrice: number;
  minimumOrderQuantity?: number | null;
  leadTimeDays?: number | null;
  isPreferred: boolean;
  isActive: boolean;
  notes?: string | null;
  createdAt: string;
  updatedAt: string;
  supplier: {
    id: string;
    name: string;
    contactPerson?: string | null;
    phone?: string | null;
    email?: string | null;
    address?: string | null;
  };
  purchaseOrderItems?: Array<{
    id: string;
    purchaseOrder: {
      id: string;
      orderDate: string;
      status: string;
      total: number;
    };
  }>;
  stockBatches?: Array<{
    id: string;
    batchNumber?: string | null;
    receivedDate: string;
    expiryDate?: string | null;
    quantity: number;
    remainingQuantity: number;
    purchasePrice: number;
  }>;
}

interface SuppliersTabProps {
  productId: string;
  productName: string;
}

export function SuppliersTab({ productId, productName }: SuppliersTabProps) {
  console.log(
    "🔥 SuppliersTab: Component rendered with productId:",
    productId,
    "productName:",
    productName
  );
  console.log("🔥 SuppliersTab: Component mounted at:", new Date().toISOString());

  const [suppliers, setSuppliers] = useState<ProductSupplier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<ProductSupplier | null>(null);
  const [deletingSupplier, setDeletingSupplier] = useState<string | null>(null);
  const [updatingPreferred, setUpdatingPreferred] = useState<string | null>(null);

  // Fetch product suppliers
  const fetchSuppliers = async () => {
    console.log("🔥 SuppliersTab: fetchSuppliers called for productId:", productId);
    setIsLoading(true);
    setError(null);

    try {
      const url = `/api/products/${productId}/suppliers`;
      console.log("🔥 SuppliersTab: Fetching from URL:", url);
      const response = await fetch(url);

      console.log("🔥 SuppliersTab: Response status:", response.status);
      console.log("🔥 SuppliersTab: Response ok:", response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("🔥 SuppliersTab: Error response:", errorText);
        throw new Error("Failed to fetch suppliers");
      }

      const data = await response.json();
      console.log("🔥 SuppliersTab: Response data:", data);
      console.log("🔥 SuppliersTab: data.suppliers:", data.suppliers);
      console.log("🔥 SuppliersTab: data.suppliers length:", data.suppliers?.length);
      setSuppliers(data.suppliers || []);
      console.log("🔥 SuppliersTab: Set suppliers:", data.suppliers || []);
      console.log("🔥 SuppliersTab: suppliers state after setting:", data.suppliers || []);
    } catch (error) {
      console.error("🔥 Error fetching suppliers:", error);
      setError("Failed to load suppliers. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log("🔥 SuppliersTab: useEffect triggered with productId:", productId);
    console.log("🔥 SuppliersTab: About to call fetchSuppliers");
    fetchSuppliers();
  }, [productId]);

  // Handle toggle preferred supplier
  const handleTogglePreferred = async (supplierId: string, currentPreferred: boolean) => {
    setUpdatingPreferred(supplierId);

    try {
      const response = await fetch(`/api/products/${productId}/suppliers/${supplierId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isPreferred: !currentPreferred,
        }),
      });

      if (!response.ok) {
        let errorMessage = "Failed to update preferred status";
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      // Parse response JSON safely
      let responseData;
      try {
        responseData = await response.json();
      } catch (jsonError) {
        // If response is not JSON, that's okay for this operation
        responseData = {};
      }

      toast.success(
        !currentPreferred ? "Supplier marked as preferred" : "Supplier removed from preferred"
      );

      // Refresh suppliers list
      await fetchSuppliers();
    } catch (error) {
      console.error("Error updating preferred status:", error);
      toast.error((error as Error).message || "Failed to update preferred status");
    } finally {
      setUpdatingPreferred(null);
    }
  };

  // Handle delete supplier
  const handleDeleteSupplier = async (supplierId: string) => {
    setDeletingSupplier(supplierId);

    try {
      const response = await fetch(`/api/products/${productId}/suppliers/${supplierId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        let errorMessage = "Failed to remove supplier";
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      // Parse response JSON safely
      let responseData;
      try {
        responseData = await response.json();
      } catch (jsonError) {
        // If response is not JSON, that's okay for this operation
        responseData = {};
      }

      toast.success("Supplier removed successfully");

      // Refresh suppliers list
      await fetchSuppliers();
    } catch (error) {
      console.error("Error removing supplier:", error);
      toast.error((error as Error).message || "Failed to remove supplier");
    } finally {
      setDeletingSupplier(null);
    }
  };

  // Handle successful add/edit
  const handleSupplierChange = () => {
    fetchSuppliers();
    setIsAddDialogOpen(false);
    setEditingSupplier(null);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading suppliers...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  console.log("🔥 SuppliersTab: Rendering with suppliers:", suppliers);
  console.log("🔥 SuppliersTab: suppliers.length:", suppliers.length);
  console.log("🔥 SuppliersTab: isLoading:", isLoading);
  console.log("🔥 SuppliersTab: error:", error);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Product Suppliers</h3>
          <p className="text-sm text-muted-foreground">Manage suppliers for {productName}</p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Supplier
        </Button>
      </div>

      {suppliers.length === 0 ? (
        <div className="text-center py-8">
          <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No suppliers found</h3>
          <p className="text-muted-foreground mb-4">
            Add suppliers to manage multiple sources for this product.
          </p>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add First Supplier
          </Button>
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead>Product Code</TableHead>
                <TableHead>Purchase Price</TableHead>
                <TableHead>MOQ</TableHead>
                <TableHead>Lead Time</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {suppliers.map((productSupplier) => (
                <TableRow key={productSupplier.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{productSupplier.supplier.name}</span>
                        {productSupplier.isPreferred && (
                          <Badge variant="default" className="text-xs">
                            <Star className="h-3 w-3 mr-1" />
                            Preferred
                          </Badge>
                        )}
                      </div>
                      {productSupplier.supplier.contactPerson && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <User className="h-3 w-3" />
                          {productSupplier.supplier.contactPerson}
                        </div>
                      )}
                      {productSupplier.supplier.phone && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Phone className="h-3 w-3" />
                          {productSupplier.supplier.phone}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">{productSupplier.supplierProductCode || "-"}</div>
                      {productSupplier.supplierProductName && (
                        <div className="text-xs text-muted-foreground">
                          {productSupplier.supplierProductName}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">
                      {formatCurrency(Number(productSupplier.purchasePrice))}
                    </span>
                  </TableCell>
                  <TableCell>
                    {productSupplier.minimumOrderQuantity
                      ? Number(productSupplier.minimumOrderQuantity)
                      : "-"}
                  </TableCell>
                  <TableCell>
                    {productSupplier.leadTimeDays ? (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        {productSupplier.leadTimeDays} days
                      </div>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant={productSupplier.isActive ? "default" : "secondary"}>
                      {productSupplier.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleTogglePreferred(
                            productSupplier.supplierId,
                            productSupplier.isPreferred
                          )
                        }
                        disabled={updatingPreferred === productSupplier.supplierId}
                        title={
                          productSupplier.isPreferred
                            ? "Remove from preferred"
                            : "Mark as preferred"
                        }
                      >
                        {updatingPreferred === productSupplier.supplierId ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : productSupplier.isPreferred ? (
                          <StarOff className="h-4 w-4" />
                        ) : (
                          <Star className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingSupplier(productSupplier)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Remove Supplier</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to remove {productSupplier.supplier.name} as a
                              supplier for this product? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteSupplier(productSupplier.supplierId)}
                              disabled={deletingSupplier === productSupplier.supplierId}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              {deletingSupplier === productSupplier.supplierId ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Removing...
                                </>
                              ) : (
                                "Remove Supplier"
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Supplier Dialog */}
      <AddSupplierDialog
        productId={productId}
        productName={productName}
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        onSuccess={handleSupplierChange}
      />

      {/* Edit Supplier Dialog */}
      {editingSupplier && (
        <EditSupplierDialog
          productId={productId}
          productName={productName}
          productSupplier={editingSupplier}
          isOpen={!!editingSupplier}
          onClose={() => setEditingSupplier(null)}
          onSuccess={handleSupplierChange}
        />
      )}
    </div>
  );
}
