"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

const editSupplierSchema = z.object({
  supplierProductCode: z.string().optional(),
  supplierProductName: z.string().optional(),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }),
  minimumOrderQuantity: z.number().min(0).optional(),
  leadTimeDays: z.number().int().min(0).optional(),
  isPreferred: z.boolean().default(false),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
});

type EditSupplierFormValues = z.infer<typeof editSupplierSchema>;

interface ProductSupplier {
  id: string;
  supplierId: string;
  supplierProductCode?: string | null;
  supplierProductName?: string | null;
  purchasePrice: number;
  minimumOrderQuantity?: number | null;
  leadTimeDays?: number | null;
  isPreferred: boolean;
  isActive: boolean;
  notes?: string | null;
  supplier: {
    id: string;
    name: string;
    contactPerson?: string | null;
    phone?: string | null;
    email?: string | null;
  };
}

interface EditSupplierDialogProps {
  productId: string;
  productName: string;
  productSupplier: ProductSupplier;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function EditSupplierDialog({
  productId,
  productName,
  productSupplier,
  isOpen,
  onClose,
  onSuccess,
}: EditSupplierDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<EditSupplierFormValues>({
    resolver: zodResolver(editSupplierSchema),
    defaultValues: {
      supplierProductCode: productSupplier.supplierProductCode || "",
      supplierProductName: productSupplier.supplierProductName || "",
      purchasePrice: Number(productSupplier.purchasePrice),
      minimumOrderQuantity: productSupplier.minimumOrderQuantity
        ? Number(productSupplier.minimumOrderQuantity)
        : 0,
      leadTimeDays: productSupplier.leadTimeDays || 0,
      isPreferred: productSupplier.isPreferred,
      isActive: productSupplier.isActive,
      notes: productSupplier.notes || "",
    },
  });

  useEffect(() => {
    if (isOpen && productSupplier) {
      form.reset({
        supplierProductCode: productSupplier.supplierProductCode || "",
        supplierProductName: productSupplier.supplierProductName || "",
        purchasePrice: Number(productSupplier.purchasePrice),
        minimumOrderQuantity: productSupplier.minimumOrderQuantity
          ? Number(productSupplier.minimumOrderQuantity)
          : 0,
        leadTimeDays: productSupplier.leadTimeDays || 0,
        isPreferred: productSupplier.isPreferred,
        isActive: productSupplier.isActive,
        notes: productSupplier.notes || "",
      });
      setError(null);
    }
  }, [isOpen, productSupplier, form]);

  const onSubmit = async (data: EditSupplierFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/products/${productId}/suppliers/${productSupplier.supplierId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            supplierProductCode: data.supplierProductCode || undefined,
            supplierProductName: data.supplierProductName || undefined,
            purchasePrice: data.purchasePrice,
            minimumOrderQuantity:
              data.minimumOrderQuantity > 0 ? data.minimumOrderQuantity : undefined,
            leadTimeDays: data.leadTimeDays > 0 ? data.leadTimeDays : undefined,
            isPreferred: data.isPreferred,
            isActive: data.isActive,
            notes: data.notes || undefined,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update supplier");
      }

      toast.success("Supplier updated successfully");
      onSuccess();
    } catch (error) {
      console.error("Error updating supplier:", error);
      setError((error as Error).message || "Failed to update supplier");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Supplier</DialogTitle>
          <DialogDescription>
            Edit supplier information for {productName} - {productSupplier.supplier.name}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Supplier Product Code */}
              <FormField
                control={form.control}
                name="supplierProductCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier Product Code</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter supplier's SKU/code" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Supplier Product Name */}
              <FormField
                control={form.control}
                name="supplierProductName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier Product Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter supplier's product name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Purchase Price */}
              <FormField
                control={form.control}
                name="purchasePrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Purchase Price *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Minimum Order Quantity */}
              <FormField
                control={form.control}
                name="minimumOrderQuantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Order Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="Optional"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Lead Time Days */}
              <FormField
                control={form.control}
                name="leadTimeDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lead Time (Days)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="Optional"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status Checkboxes */}
              <div className="md:col-span-2 space-y-3">
                <FormField
                  control={form.control}
                  name="isPreferred"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Preferred Supplier</FormLabel>
                        <p className="text-xs text-muted-foreground">
                          Mark this supplier as preferred for this product
                        </p>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active</FormLabel>
                        <p className="text-xs text-muted-foreground">
                          Enable this supplier relationship
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              {/* Notes */}
              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Optional notes about this supplier relationship"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  "Update Supplier"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
