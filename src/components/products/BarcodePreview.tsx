"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON><PERSON>, Printer, Download, Loader2 } from "lucide-react";
import { BarcodeDisplay } from "@/components/products/BarcodeDisplay";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import JsBarcode from "jsbarcode";

interface Product {
  id: string;
  name: string;
  sku: string;
  barcode: string | null;
}

interface BarcodeGeneratorOptions {
  displayProductName: boolean;
  compactLayout: boolean;
  addPageBreaks: boolean;
}

interface BarcodePreviewProps {
  products: Product[];
  mode: "multiple" | "single";
  copies: number;
  options: BarcodeGeneratorOptions;
  onBack: () => void;
}

export function BarcodePreview({ products, mode, copies, options, onBack }: BarcodePreviewProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  // Generate barcode items based on mode and copies
  const generateBarcodeItems = () => {
    if (mode === "multiple") {
      return products;
    } else if (mode === "single" && products.length > 0) {
      // Generate multiple copies of the first selected product
      return Array(copies).fill(products[0]);
    }
    return [];
  };

  const barcodeItems = generateBarcodeItems();

  // Handle print
  const handlePrint = async () => {
    setLoading(true);
    setError(null);

    try {
      // Pre-generate barcode images
      const items = barcodeItems;
      const barcodeImages: string[] = [];

      // Create a temporary canvas element to generate barcodes
      const tempCanvas = document.createElement("canvas");
      document.body.appendChild(tempCanvas);

      try {
        // Generate barcode images for each product
        for (const product of items) {
          if (product.barcode) {
            try {
              // Clear previous barcode
              const ctx = tempCanvas.getContext("2d");
              if (ctx) {
                ctx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
              }

              // Set canvas size
              tempCanvas.width = 300;
              tempCanvas.height = 100;

              // Generate barcode using JsBarcode
              JsBarcode(tempCanvas, product.barcode, {
                format: "EAN13",
                width: 2,
                height: 50,
                displayValue: true,
                fontSize: 12,
                margin: 5,
                background: "#FFFFFF",
                lineColor: "#000000",
              });

              // Convert canvas to image data
              const imgData = tempCanvas.toDataURL("image/png");
              barcodeImages.push(imgData);
            } catch (err) {
              console.error("Error generating barcode image:", err);
              barcodeImages.push(""); // Push empty string as placeholder
            }
          } else {
            barcodeImages.push(""); // Push empty string for products without barcodes
          }
        }
      } finally {
        // Clean up temporary canvas
        if (document.body.contains(tempCanvas)) {
          document.body.removeChild(tempCanvas);
        }
      }

      // Create a new window for printing
      const printWindow = window.open("", "_blank");

      if (!printWindow) {
        throw new Error("Unable to open print window. Please check your popup blocker settings.");
      }

      // Write HTML content to the new window with inline images
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Barcode Sheet</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
            }
            h1 {
              text-align: center;
              margin-bottom: 20px;
            }
            .barcode-grid {
              display: grid;
              grid-template-columns: repeat(${options.compactLayout ? 5 : 3}, 1fr);
              gap: 15px;
            }
            .barcode-item {
              border: 1px solid #e5e7eb;
              border-radius: 4px;
              padding: 10px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            }
            .product-name {
              font-size: 12px;
              text-align: center;
              margin-bottom: 5px;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .barcode-image {
              max-width: 100%;
              height: auto;
            }
            @media print {
              body {
                padding: 0;
              }
              h1 {
                margin-top: 0;
              }
              .page-break {
                page-break-after: always;
              }
            }
          </style>
        </head>
        <body>
          <h1>Barcode Sheet</h1>
          <div class="barcode-grid">
            ${items
              .map(
                (product, index) => `
              <div class="barcode-item">
                ${options.displayProductName ? `<div class="product-name">${product.name}</div>` : ""}
                <div class="barcode-container">
                  ${
                    barcodeImages[index]
                      ? `<img class="barcode-image" src="${barcodeImages[index]}" alt="${product.barcode || ""}" />`
                      : `<div>${product.barcode || ""}</div>`
                  }
                </div>
              </div>
              ${
                options.addPageBreaks && (index + 1) % 30 === 0
                  ? '<div class="page-break"></div>'
                  : ""
              }
            `
              )
              .join("")}
          </div>
          <script>
            // Print automatically after the page loads
            window.onload = function() {
              setTimeout(function() {
                window.print();
                // Uncomment to close window after printing
                // window.close();
              }, 500);
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();
      setLoading(false);
    } catch (err) {
      console.error("Error preparing print:", err);
      setError("Failed to prepare print document. Please try again.");
      setLoading(false);
    }
  };

  // Handle download as PDF
  const handleDownloadPDF = async () => {
    if (!previewRef.current) return;

    setLoading(true);
    setError(null);

    try {
      // Skip the html2canvas method entirely and use the direct method
      // which doesn't have issues with oklch colors
      await generatePDFDirectly();
    } catch (err) {
      console.error("Error generating PDF:", err);
      setError("Failed to generate PDF. Please try again or use the print function instead.");
    } finally {
      setLoading(false);
    }
  };

  // We're no longer using the html2canvas method due to oklch color issues
  // This is kept as a comment for reference
  /*
  const generatePDFWithHtml2Canvas = async () => {
    // Implementation removed to avoid oklch color issues
  };
  */

  // Direct PDF generation method that creates a PDF with barcodes
  const generatePDFDirectly = async () => {
    if (!previewRef.current) return;

    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4",
    });

    // Set up PDF
    pdf.setFontSize(16);
    pdf.text("Barcode Sheet", 105, 15, { align: "center" });

    // Calculate grid dimensions
    const pageWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const margin = 10;
    const cols = options.compactLayout ? 4 : 3;
    const cellWidth = (pageWidth - margin * 2) / cols;
    const cellHeight = options.compactLayout ? 40 : 50;

    // Process items sequentially with async/await to handle barcode generation
    const items = barcodeItems;
    let row = 0;
    let col = 0;
    let currentPage = 1;

    // Create temporary canvas for barcode generation
    const tempCanvas = document.createElement("canvas");
    document.body.appendChild(tempCanvas);

    try {
      for (const product of items) {
        if (!product.barcode) continue;

        // Calculate position
        const x = margin + col * cellWidth;
        const y = 30 + row * cellHeight;

        // Add product name if option is enabled
        if (options.displayProductName) {
          pdf.setFontSize(8);
          pdf.text(
            product.name.substring(0, 20) + (product.name.length > 20 ? "..." : ""),
            x + cellWidth / 2,
            y,
            { align: "center" }
          );
        }

        // Generate barcode on canvas
        try {
          // Clear previous barcode
          const ctx = tempCanvas.getContext("2d");
          if (ctx) {
            ctx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
          }

          // Set canvas size
          tempCanvas.width = 300;
          tempCanvas.height = 100;

          // Set canvas background to white
          if (ctx) {
            ctx.fillStyle = "#FFFFFF";
            ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
          }

          // Generate barcode using JsBarcode
          JsBarcode(tempCanvas, product.barcode, {
            format: "EAN13",
            width: 2,
            height: 50,
            displayValue: true,
            fontSize: 12,
            margin: 5,
            background: "#FFFFFF",
            lineColor: "#000000",
          });

          // Convert canvas to image data
          const imgData = tempCanvas.toDataURL("image/png");

          // Add barcode image to PDF
          const barcodeWidth = cellWidth - 10; // Slightly smaller than cell
          const barcodeHeight = 25;
          pdf.addImage(
            imgData,
            "PNG",
            x + (cellWidth - barcodeWidth) / 2,
            y + (options.displayProductName ? 5 : 0),
            barcodeWidth,
            barcodeHeight
          );
        } catch (err) {
          console.error("Error generating barcode for PDF:", err);
          // Fallback to text if barcode generation fails
          pdf.setFontSize(8);
          pdf.text(product.barcode, x + cellWidth / 2, y + 15, { align: "center" });
        }

        // Move to next position
        col++;
        if (col >= cols) {
          col = 0;
          row++;
        }

        // Add new page if needed
        if (options.addPageBreaks && row > 5) {
          pdf.addPage();
          currentPage++;
          row = 0;
        }
      }

      // Save the PDF
      pdf.save("barcodes.pdf");
    } catch (err) {
      console.error("Error in PDF generation:", err);
      throw err;
    } finally {
      // Clean up
      if (document.body.contains(tempCanvas)) {
        document.body.removeChild(tempCanvas);
      }
    }
  };

  // Calculate grid columns based on layout option
  const gridCols = options.compactLayout ? "grid-cols-5" : "grid-cols-3";
  const barcodeHeight = options.compactLayout ? 40 : 60;
  const barcodeWidth = options.compactLayout ? 1 : 1.5;
  const fontSize = options.compactLayout ? 8 : 12;

  return (
    <div className="space-y-6">
      <div className="sticky top-0 z-10 bg-background py-4 flex justify-between items-center print:hidden">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Editor
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleDownloadPDF} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Download as PDF
              </>
            )}
          </Button>
          <Button onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="print:hidden">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardContent className="p-6">
          <div
            ref={previewRef}
            className={`grid ${gridCols} gap-4 print:gap-2 ${
              options.addPageBreaks ? "print:grid-cols-3" : ""
            }`}
          >
            {barcodeItems.map((product, index) => (
              <div
                key={`${product.id}-${index}`}
                className={`flex flex-col items-center justify-center p-4 border rounded-md ${
                  options.addPageBreaks && (index + 1) % 30 === 0 ? "print:break-after-page" : ""
                }`}
              >
                {product.barcode ? (
                  <>
                    <BarcodeDisplay
                      value={product.barcode}
                      height={barcodeHeight}
                      width={barcodeWidth}
                      fontSize={fontSize}
                      displayValue={true}
                      margin={5}
                    />
                    {options.displayProductName && (
                      <div className="mt-2 text-center text-xs truncate max-w-full">
                        {product.name}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center text-muted-foreground">
                    No barcode available for {product.name}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <style jsx global>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
          }

          @page {
            size: A4;
            margin: 10mm;
          }

          .print\\:break-after-page {
            break-after: page;
          }
        }
      `}</style>
    </div>
  );
}
