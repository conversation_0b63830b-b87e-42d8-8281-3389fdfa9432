"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Barcode, CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ImageUpload } from "@/components/products/ImageUpload";
import { cn } from "@/lib/utils";

// Product schema for validation
const productSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  description: z.string().optional().nullable(),
  sku: z.string().min(1, { message: "SKU is required" }),
  barcode: z.string().min(1, { message: "Barcode is required" }),
  categoryId: z
    .union([z.literal("none"), z.string()])
    .optional()
    .nullable(),
  unitId: z.string({ required_error: "Unit is required" }),
  supplierId: z
    .union([z.literal("none"), z.string()])
    .optional()
    .nullable(),
  basePrice: z.coerce.number().positive({ message: "Base price must be positive" }),
  purchasePrice: z.coerce.number().positive({ message: "Purchase price must be positive" }),
  optionalPrice1: z
    .union([z.string(), z.number(), z.null(), z.undefined()])
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null || val === undefined || val === "") return null;
      const num = Number(val);
      return isNaN(num) ? null : num;
    })
    .refine((val) => val === null || val > 0, {
      message: "Optional price 1 must be positive",
    }),
  optionalPrice2: z
    .union([z.string(), z.number(), z.null(), z.undefined()])
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null || val === undefined || val === "") return null;
      const num = Number(val);
      return isNaN(num) ? null : num;
    })
    .refine((val) => val === null || val > 0, {
      message: "Optional price 2 must be positive",
    }),
  discountValue: z
    .union([z.string(), z.number(), z.null(), z.undefined()])
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null || val === undefined || val === "") return null;
      const num = Number(val);
      return isNaN(num) ? null : num;
    })
    .refine((val) => val === null || val >= 0, {
      message: "Discount value must be non-negative",
    }),
  discountType: z.enum(["FIXED", "PERCENTAGE"]).optional().nullable(),
  expiryDate: z.date().optional().nullable(),
  quantity: z
    .union([z.string(), z.number(), z.null(), z.undefined()])
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null || val === undefined || val === "") return null;
      const num = Number(val);
      return isNaN(num) ? null : num;
    })
    .refine((val) => val === null || val >= 0, {
      message: "Quantity cannot be negative",
    }),
  imageUrl: z.string().optional().nullable(),
  active: z.boolean().default(true),
});

export type ProductFormValues = z.infer<typeof productSchema>;

interface Category {
  id: string;
  name: string;
}

interface Unit {
  id: string;
  name: string;
  abbreviation: string;
}

interface Supplier {
  id: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
}

interface FieldError {
  field: string;
  message: string;
}

interface SubmitResult {
  success: boolean;
  fieldError?: FieldError;
}

interface ProductFormProps {
  initialData?: ProductFormValues;
  onSubmit: (data: ProductFormValues) => Promise<SubmitResult | void>;
  isSubmitting?: boolean;
  error?: string | null;
}

// Function to generate a random 13-digit barcode (EAN-13 format)
const generateBarcode = (): string => {
  // Generate 12 random digits
  let barcode = "";
  for (let i = 0; i < 12; i++) {
    barcode += Math.floor(Math.random() * 10).toString();
  }

  // Calculate the check digit (13th digit) according to EAN-13 algorithm
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(barcode[i]) * (i % 2 === 0 ? 1 : 3);
  }
  const checkDigit = (10 - (sum % 10)) % 10;

  // Return the complete 13-digit barcode
  return barcode + checkDigit.toString();
};

export function ProductForm({
  initialData,
  onSubmit,
  isSubmitting = false,
  error = null,
}: ProductFormProps) {
  // Log the initial data to see what we're working with
  console.log("ProductForm initialData:", initialData);
  const [categories, setCategories] = useState<Category[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);

  // Initialize form with default values or initial data
  const defaultValues = {
    name: "",
    description: "",
    sku: "",
    barcode: generateBarcode(), // Generate a default barcode
    categoryId: "none",
    unitId: "", // This will be set after units are loaded
    supplierId: "none",
    basePrice: 0,
    purchasePrice: 0,
    optionalPrice1: null,
    optionalPrice2: null,
    discountValue: null,
    discountType: null,
    expiryDate: null,
    quantity: null,
    imageUrl: "",
    active: true,
  };

  // If we have initial data, log the quantity specifically
  if (initialData) {
    console.log("Initial quantity value:", initialData.quantity);
  }

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: initialData || defaultValues,
  });

  // Update form values when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log("Initial data in useEffect:", initialData);

      // Handle quantity specifically
      if (initialData.quantity !== undefined) {
        const quantityValue = Number(initialData.quantity);
        console.log("Setting quantity in useEffect:", quantityValue);
        form.setValue("quantity", quantityValue);
      } else if (
        initialData.storeStock &&
        Array.isArray(initialData.storeStock) &&
        initialData.storeStock.length > 0
      ) {
        // Try to get quantity from storeStock if it exists
        const quantityValue = Number(initialData.storeStock[0].quantity || 0);
        console.log("Setting quantity from storeStock in useEffect:", quantityValue);
        form.setValue("quantity", quantityValue);
      }
    }
  }, [initialData, form]);

  // Fetch categories, units, and suppliers on component mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setLoadError(null);

      try {
        // Fetch categories
        const categoriesResponse = await fetch("/api/categories");
        if (!categoriesResponse.ok) {
          throw new Error("Failed to fetch categories");
        }
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories);

        // Fetch units
        const unitsResponse = await fetch("/api/units");
        if (!unitsResponse.ok) {
          throw new Error("Failed to fetch units");
        }
        const unitsData = await unitsResponse.json();
        const loadedUnits = unitsData.units;
        setUnits(loadedUnits);

        // Set default unit if no initial data and units are available
        if (!initialData && loadedUnits.length > 0 && !form.getValues("unitId")) {
          console.log("Setting default unit:", loadedUnits[0].id);
          form.setValue("unitId", loadedUnits[0].id);
        }

        // Fetch suppliers
        const suppliersResponse = await fetch("/api/suppliers");
        if (!suppliersResponse.ok) {
          throw new Error("Failed to fetch suppliers");
        }
        const suppliersData = await suppliersResponse.json();
        setSuppliers(suppliersData.suppliers);
      } catch (error) {
        console.error("Error fetching form data:", error);
        setLoadError("Failed to load form data. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [form, initialData]);

  // Handle form submission
  const handleSubmit = async (data: ProductFormValues) => {
    const result = await onSubmit(data);

    // Handle field-specific errors
    if (result && !result.success && result.fieldError) {
      const { field, message } = result.fieldError;
      form.setError(field as any, {
        type: "manual",
        message,
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading form data...</span>
      </div>
    );
  }

  if (loadError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{loadError}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Product Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter product name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* SKU */}
          <FormField
            control={form.control}
            name="sku"
            render={({ field }) => (
              <FormItem>
                <FormLabel>SKU</FormLabel>
                <FormControl>
                  <Input placeholder="Enter SKU" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Barcode */}
          <FormField
            control={form.control}
            name="barcode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Barcode</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input placeholder="Enter barcode" {...field} value={field.value ?? ""} />
                  </FormControl>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const newBarcode = generateBarcode();
                      field.onChange(newBarcode);
                    }}
                    title="Generate random barcode"
                  >
                    <Barcode className="h-4 w-4" />
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Category */}
          <FormField
            control={form.control}
            name="categoryId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category (Optional)</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value === "none" ? null : value);
                  }}
                  value={field.value ?? "none"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">Uncategorized</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Unit */}
          <FormField
            control={form.control}
            name="unitId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unit</FormLabel>
                {units.length === 0 ? (
                  <Alert variant="destructive" className="mt-2">
                    <AlertDescription>
                      No units available. Please create a unit first.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a unit" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {units.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          {unit.name} ({unit.abbreviation})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Supplier */}
          <FormField
            control={form.control}
            name="supplierId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Supplier (Optional)</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value === "none" ? null : value);
                  }}
                  value={field.value ?? "none"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a supplier" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">No Supplier</SelectItem>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Base Price */}
          <FormField
            control={form.control}
            name="basePrice"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Base Price</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter base price"
                    {...field}
                    onChange={(e) => field.onChange(e.target.valueAsNumber)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Purchase Price */}
          <FormField
            control={form.control}
            name="purchasePrice"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Purchase Price</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter purchase price"
                    {...field}
                    onChange={(e) => field.onChange(e.target.valueAsNumber)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Optional Price 1 */}
          <FormField
            control={form.control}
            name="optionalPrice1"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Optional Price 1</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter optional price 1"
                    {...field}
                    value={field.value ?? ""}
                    onChange={(e) => {
                      const value = e.target.value === "" ? null : e.target.valueAsNumber;
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Optional Price 2 */}
          <FormField
            control={form.control}
            name="optionalPrice2"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Optional Price 2</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter optional price 2"
                    {...field}
                    value={field.value ?? ""}
                    onChange={(e) => {
                      const value = e.target.value === "" ? null : e.target.valueAsNumber;
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Discount Value */}
          <FormField
            control={form.control}
            name="discountValue"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Discount Value</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter discount value"
                    {...field}
                    value={field.value ?? ""}
                    onChange={(e) => {
                      const value = e.target.value === "" ? null : e.target.valueAsNumber;
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Discount Type */}
          <FormField
            control={form.control}
            name="discountType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Discount Type</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value === "none" ? null : value);
                  }}
                  value={field.value ?? "none"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select discount type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">No Discount</SelectItem>
                    <SelectItem value="FIXED">Fixed Amount</SelectItem>
                    <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Expiry Date */}
          <FormField
            control={form.control}
            name="expiryDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Expiry Date (Optional)</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick an expiry date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date() || date < new Date("1900-01-01")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Quantity */}
          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Initial Stock Quantity</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter initial quantity"
                    {...field}
                    value={field.value ?? ""}
                    onChange={(e) => {
                      const value = e.target.value === "" ? null : e.target.valueAsNumber;
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <div className="md:col-span-2">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter product description"
                      {...field}
                      value={field.value ?? ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="md:col-span-2">
          <FormField
            control={form.control}
            name="imageUrl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Product Image (Optional)</FormLabel>
                <FormControl>
                  <ImageUpload value={field.value} onChange={field.onChange} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {initialData ? "Updating..." : "Creating..."}
              </>
            ) : (
              <>{initialData ? "Update Product" : "Create Product"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
