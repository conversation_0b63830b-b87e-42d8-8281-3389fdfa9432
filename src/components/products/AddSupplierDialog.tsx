"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

const addSupplierSchema = z.object({
  supplierId: z.string().min(1, { message: "Supplier is required" }),
  supplierProductCode: z.string().optional(),
  supplierProductName: z.string().optional(),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }),
  minimumOrderQuantity: z.number().min(0).optional(),
  leadTimeDays: z.number().int().min(0).optional(),
  isPreferred: z.boolean().default(false),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
});

type AddSupplierFormValues = z.infer<typeof addSupplierSchema>;

interface Supplier {
  id: string;
  name: string;
  contactPerson?: string | null;
  phone?: string | null;
  email?: string | null;
  address?: string | null;
}

interface AddSupplierDialogProps {
  productId: string;
  productName: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function AddSupplierDialog({
  productId,
  productName,
  isOpen,
  onClose,
  onSuccess,
}: AddSupplierDialogProps) {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [isLoadingSuppliers, setIsLoadingSuppliers] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<AddSupplierFormValues>({
    resolver: zodResolver(addSupplierSchema),
    defaultValues: {
      supplierId: "",
      supplierProductCode: "",
      supplierProductName: "",
      purchasePrice: 0,
      minimumOrderQuantity: 0,
      leadTimeDays: 0,
      isPreferred: false,
      isActive: true,
      notes: "",
    },
  });

  // Fetch available suppliers
  const fetchSuppliers = async () => {
    setIsLoadingSuppliers(true);
    try {
      const response = await fetch("/api/suppliers");

      if (!response.ok) {
        throw new Error("Failed to fetch suppliers");
      }

      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      toast.error("Failed to load suppliers");
    } finally {
      setIsLoadingSuppliers(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchSuppliers();
      form.reset({
        supplierId: "",
        supplierProductCode: "",
        supplierProductName: "",
        purchasePrice: 0,
        minimumOrderQuantity: 0,
        leadTimeDays: 0,
        isPreferred: false,
        isActive: true,
        notes: "",
      });
      setError(null);
    }
  }, [isOpen, form]);

  const onSubmit = async (data: AddSupplierFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(`/api/products/${productId}/suppliers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          supplierId: data.supplierId,
          supplierProductCode: data.supplierProductCode || undefined,
          supplierProductName: data.supplierProductName || undefined,
          purchasePrice: data.purchasePrice,
          minimumOrderQuantity:
            data.minimumOrderQuantity > 0 ? data.minimumOrderQuantity : undefined,
          leadTimeDays: data.leadTimeDays > 0 ? data.leadTimeDays : undefined,
          isPreferred: data.isPreferred,
          isActive: data.isActive,
          notes: data.notes || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add supplier");
      }

      toast.success("Supplier added successfully");
      onSuccess();
    } catch (error) {
      console.error("Error adding supplier:", error);
      setError((error as Error).message || "Failed to add supplier");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Supplier</DialogTitle>
          <DialogDescription>Add a new supplier for {productName}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Supplier Selection */}
              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="supplierId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supplier *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a supplier" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingSuppliers ? (
                            <div className="flex items-center justify-center p-4">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span className="ml-2">Loading suppliers...</span>
                            </div>
                          ) : suppliers.length === 0 ? (
                            <div className="p-4 text-center text-muted-foreground">
                              No suppliers found
                            </div>
                          ) : (
                            suppliers.map((supplier) => (
                              <SelectItem key={supplier.id} value={supplier.id}>
                                <div>
                                  <div className="font-medium">{supplier.name}</div>
                                  {supplier.contactPerson && (
                                    <div className="text-xs text-muted-foreground">
                                      {supplier.contactPerson}
                                    </div>
                                  )}
                                </div>
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Supplier Product Code */}
              <FormField
                control={form.control}
                name="supplierProductCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier Product Code</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter supplier's SKU/code" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Supplier Product Name */}
              <FormField
                control={form.control}
                name="supplierProductName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier Product Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter supplier's product name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Purchase Price */}
              <FormField
                control={form.control}
                name="purchasePrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Purchase Price *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Minimum Order Quantity */}
              <FormField
                control={form.control}
                name="minimumOrderQuantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Order Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="0"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Lead Time Days */}
              <FormField
                control={form.control}
                name="leadTimeDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lead Time (Days)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="0"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status Checkboxes */}
              <div className="md:col-span-2 space-y-3">
                <FormField
                  control={form.control}
                  name="isPreferred"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Preferred Supplier</FormLabel>
                        <p className="text-xs text-muted-foreground">
                          Mark this supplier as preferred for this product
                        </p>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active</FormLabel>
                        <p className="text-xs text-muted-foreground">
                          Enable this supplier relationship
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              {/* Notes */}
              <div className="md:col-span-2">
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Optional notes about this supplier relationship"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  "Add Supplier"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
