import re

# Read the file
with open('src/app/admin/analytics/page.tsx', 'r') as f:
    content = f.read()

# Add imports after OperationalMetricsCard import
import_pattern = r'(import { OperationalMetricsCard } from "./components/charts/OperationalMetricsCard";)'
import_replacement = r'\1\nimport { DailyTransactionCountChart } from "./components/charts/DailyTransactionCountChart";\nimport { DailyRevenueProfitChart } from "./components/charts/DailyRevenueProfitChart";'
content = re.sub(import_pattern, import_replacement, content)

# Add charts after DrawerSessionsChart in Sales tab
chart_pattern = r'(\s+<DrawerSessionsChart filters={getFilters\(\)} />\s+</div>)'
chart_replacement = r'\1\n\n            {/* Daily Transaction Count and Revenue vs Profit Charts */}\n            <div className="grid gap-6 md:grid-cols-2">\n              <DailyTransactionCountChart filters={getFilters()} />\n              <DailyRevenueProfitChart filters={getFilters()} />\n            </div>'
content = re.sub(chart_pattern, chart_replacement, content)

# Write the file
with open('src/app/admin/analytics/page.tsx', 'w') as f:
    f.write(content)

print("Charts added successfully!")
