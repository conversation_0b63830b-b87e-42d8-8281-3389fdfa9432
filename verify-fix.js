// Simple verification script to check if our cart fix is working
// This script simulates the addToCart behavior to test our duplicate prevention

console.log("🧪 Testing Cart Fix - Duplicate Prevention Logic");
console.log("=" .repeat(50));

// Simulate the duplicate prevention logic
class CartFixTest {
  constructor() {
    this.addToCartInProgress = new Set();
    this.lastAddToCartCall = new Map();
    this.callCount = 0;
  }

  // Simulate the enhanced addToCart logic
  addToCart(productId, productName) {
    this.callCount++;
    const timestamp = new Date().toISOString();
    const callId = `${productId}-${timestamp}`;

    console.log(`\n📞 Call #${this.callCount}: addToCart(${productId}, "${productName}")`);
    console.log(`🕐 Timestamp: ${timestamp}`);

    // Enhanced duplicate prevention with timing
    const now = Date.now();
    const lastCallTime = this.lastAddToCartCall.get(productId) || 0;
    const timeSinceLastCall = now - lastCallTime;

    console.log(`⏱️  Time since last call: ${timeSinceLastCall}ms`);

    // Prevent duplicate calls within 500ms
    if (timeSinceLastCall < 500) {
      console.log(`❌ BLOCKED: Duplicate call detected within 500ms for product ${productId}`);
      return false;
    }

    // Check if already in progress
    if (this.addToCartInProgress.has(productId)) {
      console.log(`❌ BLOCKED: AddToCart already in progress for product ${productId}`);
      return false;
    }

    // Record this call time and mark as in progress
    this.lastAddToCartCall.set(productId, now);
    this.addToCartInProgress.add(productId);
    console.log(`✅ ALLOWED: Product ${productId} added to cart`);

    // Simulate cleanup after 500ms
    setTimeout(() => {
      this.addToCartInProgress.delete(productId);
      this.lastAddToCartCall.delete(productId);
      console.log(`🧹 CLEANUP: Cleared tracking for product ${productId}`);
    }, 500);

    return true;
  }

  // Helper to wait
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Run comprehensive tests
  async runTests() {
    console.log("\n🧪 TEST 1: Normal product addition");
    this.addToCart("PROD-001", "Test Product A");

    console.log("\n🧪 TEST 2: Immediate duplicate call (should be blocked)");
    this.addToCart("PROD-001", "Test Product A");

    console.log("\n🧪 TEST 3: Rapid successive calls (should be blocked)");
    this.addToCart("PROD-001", "Test Product A");
    await this.wait(100);
    this.addToCart("PROD-001", "Test Product A");

    console.log("\n🧪 TEST 4: Different product (should be allowed)");
    this.addToCart("PROD-002", "Test Product B");

    console.log("\n🧪 TEST 5: Same product after 600ms delay (should be allowed)");
    await this.wait(600);
    this.addToCart("PROD-001", "Test Product A");

    console.log("\n🧪 TEST 6: React Strict Mode simulation (double call)");
    // Simulate React Strict Mode calling the function twice
    const productId = "PROD-003";
    const productName = "Test Product C";
    console.log("Simulating React Strict Mode double invocation...");
    this.addToCart(productId, productName);
    this.addToCart(productId, productName); // Immediate duplicate

    console.log("\n" + "=" .repeat(50));
    console.log("🎉 Test completed! Check the results above.");
    console.log("✅ Expected: Only legitimate calls should be ALLOWED");
    console.log("❌ Expected: Duplicate/rapid calls should be BLOCKED");
  }
}

// Run the tests
const tester = new CartFixTest();
tester.runTests().then(() => {
  console.log("\n🏁 All tests completed!");
}).catch(error => {
  console.error("❌ Test failed:", error);
});
