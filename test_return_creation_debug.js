// Use built-in fetch (Node.js 18+) or import fetch
const fetch = globalThis.fetch || (await import('node-fetch')).default;

async function testReturnCreation() {
  console.log('🔍 Testing Return Creation API...\n');
  
  try {
    // First, get a transaction to test with
    console.log('1. Fetching transactions...');
    const transactionsResponse = await fetch('http://localhost:3000/api/transactions?status=COMPLETED&limit=5');
    
    if (!transactionsResponse.ok) {
      console.error('❌ Failed to fetch transactions:', transactionsResponse.status);
      return;
    }
    
    const transactionsData = await transactionsResponse.json();
    console.log(`✅ Found ${transactionsData.transactions.length} completed transactions`);
    
    if (transactionsData.transactions.length === 0) {
      console.error('❌ No completed transactions found to test with');
      return;
    }
    
    // Test with the first transaction
    const transaction = transactionsData.transactions[0];
    console.log(`\n2. Testing with transaction: ${transaction.id}`);
    console.log(`   Customer: ${transaction.customer?.name || 'Walk-in Customer'}`);
    console.log(`   Customer ID: ${transaction.customerId || 'null'}`);
    console.log(`   Items: ${transaction.items.length}`);
    
    // Create test return data
    const returnData = {
      transactionId: transaction.id,
      customerId: transaction.customerId, // This could be null for walk-in customers
      reason: "Test return - API debugging",
      notes: "Testing return creation after schema fix",
      items: transaction.items.slice(0, 1).map(item => ({
        productId: item.productId,
        quantity: 1, // Return 1 unit
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.unitPrice) * 1,
      })),
    };
    
    console.log('\n3. Return data to be sent:');
    console.log(JSON.stringify(returnData, null, 2));
    
    // Test the return creation
    console.log('\n4. Creating return...');
    const returnResponse = await fetch('http://localhost:3000/api/returns', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(returnData),
    });
    
    console.log(`   Response status: ${returnResponse.status}`);
    
    if (returnResponse.ok) {
      const result = await returnResponse.json();
      console.log('✅ Return created successfully!');
      console.log(`   Return ID: ${result.id}`);
      console.log(`   Status: ${result.status}`);
      console.log(`   Customer ID: ${result.customerId || 'null'}`);
    } else {
      const error = await returnResponse.text();
      console.error('❌ Return creation failed:');
      console.error(`   Status: ${returnResponse.status}`);
      console.error(`   Error: ${error}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Test with both scenarios
async function runTests() {
  console.log('🚀 Starting Return Creation Debug Tests\n');
  console.log('=' .repeat(50));
  
  await testReturnCreation();
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 Test completed');
}

runTests();
