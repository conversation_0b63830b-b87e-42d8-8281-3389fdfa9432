/**
 * Purchase Order Integration Testing Script
 * Tests enhanced PO creation and receiving with multi-supplier functionality
 */

const BASE_URL = 'http://localhost:3001';
let authToken = null;
let testData = {
  productId: null,
  supplierId: null,
  productSupplierId: null,
  purchaseOrderId: null,
  stockBatchId: null
};

// Helper function to make authenticated requests
async function makeRequest(method, endpoint, data = null, expectedStatus = 200) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': authToken ? `session-token=${authToken}` : ''
    }
  };

  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    options.body = JSON.stringify(data);
  }

  console.log(`\n🔄 ${method} ${endpoint}`);
  if (data) console.log('📤 Request:', JSON.stringify(data, null, 2));

  try {
    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let parsedData;
    try {
      parsedData = JSON.parse(responseData);
    } catch (e) {
      parsedData = responseData;
    }

    console.log(`📊 Status: ${response.status} (Expected: ${expectedStatus})`);
    
    if (response.status === expectedStatus) {
      console.log('✅ Success');
      if (parsedData && typeof parsedData === 'object') {
        console.log('📥 Response keys:', Object.keys(parsedData));
        if (parsedData.id) console.log('🆔 ID:', parsedData.id);
      }
      return { success: true, data: parsedData, status: response.status };
    } else {
      console.log('❌ Failed');
      console.log('📥 Response:', parsedData);
      return { success: false, data: parsedData, status: response.status };
    }
  } catch (error) {
    console.log('💥 Error:', error.message);
    return { success: false, error: error.message };
  }
}

// Authentication function
async function authenticate() {
  console.log('\n🔐 Authenticating...');
  
  const loginResponse = await fetch(`${BASE_URL}/api/auth/signin`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    })
  });

  if (loginResponse.ok) {
    const cookies = loginResponse.headers.get('set-cookie');
    if (cookies) {
      const tokenMatch = cookies.match(/session-token=([^;]+)/);
      if (tokenMatch) {
        authToken = tokenMatch[1];
        console.log('✅ Authentication successful');
        return true;
      }
    }
  }

  console.log('❌ Authentication failed');
  return false;
}

// Setup test data and relationships
async function setupTestData() {
  console.log('\n📋 Setting up test data and relationships...');

  // Get existing product and supplier
  const productsResponse = await makeRequest('GET', '/api/products?limit=1');
  if (productsResponse.success && productsResponse.data.products?.length > 0) {
    testData.productId = productsResponse.data.products[0].id;
    console.log(`✅ Using product ID: ${testData.productId}`);
  }

  const suppliersResponse = await makeRequest('GET', '/api/suppliers?limit=1');
  if (suppliersResponse.success && suppliersResponse.data.suppliers?.length > 0) {
    testData.supplierId = suppliersResponse.data.suppliers[0].id;
    console.log(`✅ Using supplier ID: ${testData.supplierId}`);
  }

  if (!testData.productId || !testData.supplierId) {
    console.log('❌ Failed to get test data');
    return false;
  }

  // Create ProductSupplier relationship if it doesn't exist
  const existingRelationship = await makeRequest('GET', `/api/products/${testData.productId}/suppliers/${testData.supplierId}`, null, 200);
  
  if (!existingRelationship.success) {
    console.log('🔗 Creating ProductSupplier relationship...');
    const createRelationshipResponse = await makeRequest('POST', `/api/products/${testData.productId}/suppliers`, {
      supplierId: testData.supplierId,
      supplierProductCode: 'PO-TEST-SKU-001',
      supplierProductName: 'PO Test Product',
      purchasePrice: 15.99,
      minimumOrderQuantity: 5,
      leadTimeDays: 5,
      isPreferred: true,
      isActive: true,
      notes: 'Test relationship for PO integration'
    }, 201);

    if (createRelationshipResponse.success) {
      testData.productSupplierId = createRelationshipResponse.data.productSupplier?.id;
      console.log(`✅ Created ProductSupplier relationship: ${testData.productSupplierId}`);
    } else {
      console.log('❌ Failed to create ProductSupplier relationship');
      return false;
    }
  } else {
    testData.productSupplierId = existingRelationship.data.productSupplier?.id;
    console.log(`✅ Using existing ProductSupplier relationship: ${testData.productSupplierId}`);
  }

  return true;
}

// Test enhanced purchase order creation
async function testEnhancedPOCreation() {
  console.log('\n🧪 Testing Enhanced Purchase Order Creation...');

  const poData = {
    supplierId: testData.supplierId,
    orderDate: new Date().toISOString(),
    notes: 'Test PO for multi-supplier integration',
    taxPercentage: 10,
    items: [
      {
        productId: testData.productId,
        quantity: 20,
        unitPrice: 15.99
      }
    ]
  };

  const createPOResponse = await makeRequest('POST', '/api/purchase-orders', poData, 201);

  if (createPOResponse.success) {
    testData.purchaseOrderId = createPOResponse.data.id;
    console.log(`✅ Created PO: ${testData.purchaseOrderId}`);

    // Verify PO has ProductSupplier information
    const poDetails = await makeRequest('GET', `/api/purchase-orders/${testData.purchaseOrderId}`);
    if (poDetails.success) {
      const items = poDetails.data.items || [];
      const hasProductSupplierInfo = items.some(item => item.productSupplierId);
      console.log(`📊 PO Items with ProductSupplier info: ${hasProductSupplierInfo ? 'Yes' : 'No'}`);
      
      if (hasProductSupplierInfo) {
        console.log('✅ ProductSupplier linking in PO creation works correctly');
      } else {
        console.log('⚠️ ProductSupplier linking may not be working');
      }
    }
  } else {
    console.log('❌ Failed to create purchase order');
    return false;
  }

  return true;
}

// Test purchase order approval workflow
async function testPOApprovalWorkflow() {
  console.log('\n🧪 Testing Purchase Order Approval Workflow...');

  if (!testData.purchaseOrderId) {
    console.log('❌ No PO ID available for approval testing');
    return false;
  }

  // Approve the purchase order
  const approveResponse = await makeRequest('PATCH', `/api/purchase-orders/${testData.purchaseOrderId}`, {
    status: 'APPROVED',
    notes: 'Approved for multi-supplier testing'
  });

  if (approveResponse.success) {
    console.log('✅ Purchase order approved successfully');
    
    // Verify status change
    const poDetails = await makeRequest('GET', `/api/purchase-orders/${testData.purchaseOrderId}`);
    if (poDetails.success && poDetails.data.status === 'APPROVED') {
      console.log('✅ PO status updated correctly');
    } else {
      console.log('⚠️ PO status may not have updated correctly');
    }
  } else {
    console.log('❌ Failed to approve purchase order');
    return false;
  }

  return true;
}

// Test enhanced purchase order receiving with StockBatch creation
async function testEnhancedPOReceiving() {
  console.log('\n🧪 Testing Enhanced Purchase Order Receiving...');

  if (!testData.purchaseOrderId) {
    console.log('❌ No PO ID available for receiving testing');
    return false;
  }

  // Get PO details to find item IDs
  const poDetails = await makeRequest('GET', `/api/purchase-orders/${testData.purchaseOrderId}`);
  if (!poDetails.success || !poDetails.data.items?.length) {
    console.log('❌ Cannot get PO items for receiving');
    return false;
  }

  const firstItem = poDetails.data.items[0];
  console.log(`📦 Receiving item: ${firstItem.id} (Quantity: ${firstItem.quantity})`);

  // Receive the purchase order
  const receiveData = {
    notes: 'Test receiving for multi-supplier integration',
    items: [
      {
        purchaseOrderItemId: firstItem.id,
        receivedQuantity: Number(firstItem.quantity),
        notes: 'Full quantity received'
      }
    ]
  };

  const receiveResponse = await makeRequest('POST', `/api/purchase-orders/${testData.purchaseOrderId}/receive`, receiveData);

  if (receiveResponse.success) {
    console.log('✅ Purchase order received successfully');

    // Check if StockBatch was created
    const stockBatchesResponse = await makeRequest('GET', `/api/stock-batches?productId=${testData.productId}`);
    if (stockBatchesResponse.success) {
      const batches = stockBatchesResponse.data.stockBatches || [];
      const newBatch = batches.find(batch => batch.purchaseOrderId === testData.purchaseOrderId);
      
      if (newBatch) {
        testData.stockBatchId = newBatch.id;
        console.log(`✅ StockBatch created automatically: ${testData.stockBatchId}`);
        console.log(`📊 Batch details: ${newBatch.batchNumber}, Qty: ${newBatch.quantity}, Price: ${newBatch.purchasePrice}`);
        
        // Verify batch has supplier information
        if (newBatch.productSupplierId) {
          console.log('✅ StockBatch has ProductSupplier linkage');
        } else {
          console.log('⚠️ StockBatch missing ProductSupplier linkage');
        }
      } else {
        console.log('⚠️ StockBatch may not have been created automatically');
      }
    }

    // Verify PO status updated
    const updatedPODetails = await makeRequest('GET', `/api/purchase-orders/${testData.purchaseOrderId}`);
    if (updatedPODetails.success) {
      console.log(`📊 PO Status after receiving: ${updatedPODetails.data.status}`);
      if (updatedPODetails.data.status === 'RECEIVED') {
        console.log('✅ PO status updated to RECEIVED correctly');
      }
    }
  } else {
    console.log('❌ Failed to receive purchase order');
    return false;
  }

  return true;
}

// Test stock history integration
async function testStockHistoryIntegration() {
  console.log('\n🧪 Testing Stock History Integration...');

  if (!testData.productId) {
    console.log('❌ No product ID available for stock history testing');
    return false;
  }

  // Get stock history for the product
  const stockHistoryResponse = await makeRequest('GET', `/api/products/${testData.productId}/stock-history`);
  
  // If the endpoint doesn't exist, try a different approach
  if (!stockHistoryResponse.success) {
    console.log('ℹ️ Direct stock history endpoint not available, checking through product details');
    
    const productDetails = await makeRequest('GET', `/api/products/${testData.productId}`);
    if (productDetails.success) {
      console.log('✅ Product details retrieved successfully');
      // Check if product has supplier and batch information
      const hasSuppliers = productDetails.data.productSuppliers?.length > 0;
      const hasBatches = productDetails.data.stockBatches?.length > 0;
      
      console.log(`📊 Product has suppliers: ${hasSuppliers ? 'Yes' : 'No'}`);
      console.log(`📊 Product has stock batches: ${hasBatches ? 'Yes' : 'No'}`);
      
      if (hasSuppliers && hasBatches) {
        console.log('✅ Multi-supplier integration in product data works correctly');
      }
    }
  } else {
    console.log('✅ Stock history retrieved successfully');
  }

  return true;
}

// Main test execution for PO integration
async function runPOIntegrationTests() {
  console.log('🚀 Starting Purchase Order Integration Testing...');
  console.log('=' .repeat(60));

  // Step 1: Authenticate
  const authSuccess = await authenticate();
  if (!authSuccess) {
    console.log('💥 Cannot proceed without authentication');
    return;
  }

  // Step 2: Setup test data
  const setupSuccess = await setupTestData();
  if (!setupSuccess) {
    console.log('💥 Cannot proceed without test data');
    return;
  }

  // Step 3: Run integration tests
  try {
    const poCreated = await testEnhancedPOCreation();
    if (poCreated) {
      await testPOApprovalWorkflow();
      await testEnhancedPOReceiving();
    }
    
    await testStockHistoryIntegration();

    console.log('\n🎉 Purchase Order Integration Tests completed!');
    console.log('=' .repeat(60));
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`Product ID: ${testData.productId}`);
    console.log(`Supplier ID: ${testData.supplierId}`);
    console.log(`ProductSupplier ID: ${testData.productSupplierId}`);
    console.log(`Purchase Order ID: ${testData.purchaseOrderId}`);
    console.log(`Stock Batch ID: ${testData.stockBatchId}`);

  } catch (error) {
    console.log('💥 Test execution failed:', error.message);
  }
}

// Run tests if this script is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runPOIntegrationTests().catch(console.error);
}

module.exports = { runPOIntegrationTests, makeRequest, authenticate };
