// Simple test script to test purchase order creation
const testPOCreation = async () => {
  try {
    // First, let's get real supplier and product IDs
    console.log("Fetching suppliers and products...");

    const [suppliersRes, productsRes] = await Promise.all([
      fetch("http://localhost:3001/api/suppliers"),
      fetch("http://localhost:3001/api/products")
    ]);

    const suppliersData = await suppliersRes.json();
    const productsData = await productsRes.json();

    console.log("Suppliers found:", suppliersData.suppliers?.length || 0);
    console.log("Products found:", productsData.products?.length || 0);

    if (!suppliersData.suppliers || suppliersData.suppliers.length === 0) {
      console.error("No suppliers found");
      return;
    }

    if (!productsData.products || productsData.products.length === 0) {
      console.error("No products found");
      return;
    }

    // Use real IDs
    const supplier = suppliersData.suppliers[0];
    const product = productsData.products[0];

    console.log("Using supplier:", supplier.name, "ID:", supplier.id);
    console.log("Using product:", product.name, "ID:", product.id);

    // Test payload with real IDs and new tax percentage format
    const payload = {
      supplierId: supplier.id,
      orderDate: "2025-01-07",
      tax: 5000, // 5000 IDR tax amount
      taxPercentage: 10, // 10% tax
      notes: "Test purchase order with percentage tax",
      items: [
        {
          productId: product.id,
          quantity: 2,
          unitPrice: product.purchasePrice || 25000
        }
      ]
    };

    console.log("Testing payload:", JSON.stringify(payload, null, 2));

    const response = await fetch("http://localhost:3001/api/purchase-orders", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Note: This will fail due to authentication, but we can see the validation error
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();
    console.log("Response status:", response.status);
    console.log("Response:", JSON.stringify(result, null, 2));

    if (response.status === 401) {
      console.log("✅ Expected authentication error - API is working correctly");
    } else if (response.status === 400) {
      console.log("✅ Validation error - check the validation details above");
    } else {
      console.log("❌ Unexpected response status");
    }

  } catch (error) {
    console.error("Error:", error);
  }
};

// Run the test
testPOCreation();
