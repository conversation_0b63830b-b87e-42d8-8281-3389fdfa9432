# Customer Analytics Specifications

## Overview
This document outlines the comprehensive customer analytics features to be implemented in the Customer tab of the analytics page. The analytics will provide insights into customer behavior, segmentation, and business intelligence for better customer relationship management.

## Current Customer Data Structure
Based on the existing schema, customers have the following properties:
- Basic Info: id, name, phone, email, address
- Classification: customerType (REGULAR, FRIEND, FAMILY, VIP)
- Timestamps: createdAt, updatedAt
- Relations: transactions, returns

## Proposed Customer Analytics Components

### 1. Customer Summary Cards (Top Row KPIs)

#### Total Customers Card
- Metric: Total number of customers in the system
- Growth Indicator: Percentage change from previous period
- Tooltip: "Total registered customers in your database"

#### New Customers Card
- Metric: Customers acquired in the selected date range
- Growth Indicator: Comparison with previous period
- Tooltip: "New customer registrations in the selected period"

#### Customer Retention Rate Card
- Metric: Percentage of customers who made repeat purchases
- Calculation: (Returning Customers / Total Customers) × 100
- Tooltip: "Percentage of customers who made more than one purchase"

#### Average Customer Value Card
- Metric: Average total spending per customer
- Calculation: Total Revenue / Total Customers
- Tooltip: "Average lifetime value per customer"

### 2. Customer Segmentation Visualizations

#### Customer Types Distribution (Pie Chart)
- Data: Breakdown by customerType (REGULAR, FRIEND, FAMILY, VIP)
- Metrics: Count and percentage for each type
- Interactive: Click to filter other charts by customer type

#### Customer Value Segments (Donut Chart)
- Segments:
  - High-Value: Top 20% by total spending
  - Medium-Value: Middle 60% by total spending
  - Low-Value: Bottom 20% by total spending
- Metrics: Customer count and total revenue per segment

### 3. Customer Behavior Charts

#### Top Customers by Revenue (Horizontal Bar Chart)
- Data: Top 10-15 customers by total spending
- Metrics: Customer name, total revenue, transaction count
- Interactive: Click to view customer details

#### Customer Purchase Frequency (Vertical Bar Chart)
- Data: Distribution of purchase frequency
- X-axis: Number of purchases (1, 2-5, 6-10, 11-20, 21+)
- Y-axis: Number of customers
- Tooltip: Shows customer count and percentage

#### Customer Acquisition Trends (Line Chart)
- Data: New customer registrations over time
- Time Periods: Daily, Weekly, Monthly based on date range
- Metrics: New customers per period with trend line

#### New vs Returning Customers (Area Chart)
- Data: Transaction volume split by customer type
- Categories: New customers vs Returning customers
- Time Series: Based on selected date range

### 4. Customer Insights Card

#### Customer Acquisition Metrics
- Monthly Growth Rate: Percentage change in new customers
- Acquisition Channels: If tracking source is available
- Seasonal Patterns: Best months/periods for customer acquisition

#### Purchase Pattern Analysis
- Average Items per Transaction: By customer type
- Average Transaction Value: By customer segment
- Purchase Frequency: Average days between purchases
- Preferred Payment Methods: By customer type

#### Customer Loyalty Metrics
- Repeat Purchase Rate: Percentage of customers with 2+ purchases
- Customer Lifetime: Average days from first to last purchase
- Churn Risk Indicators: Customers with no recent activity
- Loyalty Score: Based on frequency and recency

#### At-Risk Customer Identification
- Dormant Customers: No purchases in last 30/60/90 days
- Declining Customers: Decreasing purchase frequency
- One-Time Customers: Only made single purchase
- Reactivation Opportunities: Suggested actions

### 5. Advanced Customer Analytics (Future Enhancements)

#### Customer Journey Analysis
- First Purchase Analysis: Products, amounts, payment methods
- Customer Lifecycle Stages: New, Active, At-Risk, Churned
- Purchase Evolution: How customer behavior changes over time

#### Geographic Analysis (if address data available)
- Customer Distribution Map: By city/region
- Regional Performance: Revenue by geographic area
- Delivery Patterns: If applicable

#### Product Preference Analysis
- Top Products by Customer Type: Most popular items per segment
- Cross-Selling Opportunities: Products frequently bought together
- Category Preferences: Product categories by customer type
- Seasonal Preferences: How preferences change over time

#### Predictive Analytics (Advanced)
- Customer Lifetime Value Prediction: Expected future value
- Churn Prediction: Likelihood of customer becoming inactive
- Next Purchase Prediction: When customer likely to return
- Upselling Opportunities: Customers ready for higher-value products

## Technical Implementation Plan

### API Endpoints to Create
1. `/api/analytics/customer-summary` - KPI metrics
2. `/api/analytics/customer-segmentation` - Type and value distributions
3. `/api/analytics/customer-behavior` - Purchase patterns and trends
4. `/api/analytics/customer-insights` - Detailed analytics and recommendations

### Database Queries Required
- Customer counts with date filtering
- Transaction aggregations by customer
- Customer type distributions
- Purchase frequency calculations
- Customer lifetime value calculations
- Retention rate calculations

### Component Structure
```
CustomerAnalytics/
├── CustomerSummaryCards.tsx
├── CustomerSegmentationCharts.tsx
├── CustomerBehaviorCharts.tsx
├── CustomerInsightsCard.tsx
└── types/
    └── customer-analytics.ts
```

## Success Metrics
- Business Impact: Improved customer retention rates
- User Adoption: Analytics page usage by admin users
- Data Quality: Accuracy of customer insights
- Performance: Page load times under 3 seconds

## Future Considerations
- Integration with marketing campaigns
- Customer communication tracking
- Loyalty program analytics
- Customer feedback integration
- Mobile app usage analytics (if applicable)

---

**Document Status**: Draft  
**Last Updated**: December 2024  
**Next Review**: After implementation of basic customer analytics
