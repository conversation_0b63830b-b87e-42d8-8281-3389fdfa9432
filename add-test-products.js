import { PrismaClient } from './src/generated/prisma/index.js';

const prisma = new PrismaClient();

async function addTestProducts() {
  try {
    console.log('Adding test products for cart testing...');

    // First, get or create a unit
    let unit = await prisma.unit.findFirst({
      where: { name: 'Piece' }
    });

    if (!unit) {
      unit = await prisma.unit.create({
        data: {
          name: 'Piece',
          abbreviation: 'pcs',
          description: 'Individual pieces'
        }
      });
    }

    // Test products to add
    const testProducts = [
      {
        name: 'Test Product A',
        sku: 'TEST-A-001',
        barcode: '1234567890123',
        basePrice: 10000,
        stock: 50
      },
      {
        name: 'Test Product B',
        sku: 'TEST-B-002',
        barcode: '1234567890124',
        basePrice: 15000,
        stock: 30
      },
      {
        name: 'Test Product C',
        sku: 'TEST-C-003',
        barcode: '1234567890125',
        basePrice: 25000,
        stock: 20
      }
    ];

    for (const productData of testProducts) {
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: { sku: productData.sku }
      });

      if (!existingProduct) {
        // Create the product
        const product = await prisma.product.create({
          data: {
            name: productData.name,
            sku: productData.sku,
            barcode: productData.barcode,
            basePrice: productData.basePrice,
            unitId: unit.id,
            active: true
          }
        });

        // Create store stock for the product
        await prisma.storeStock.create({
          data: {
            productId: product.id,
            quantity: productData.stock,
            minThreshold: 5
          }
        });

        console.log(`✅ Created: ${productData.name} (${productData.sku})`);
      } else {
        console.log(`⏭️  Skipped: ${productData.name} (already exists)`);
      }
    }

    console.log('\n🎉 Test products setup completed!');
    console.log('\nYou can now test the cart functionality with these products:');
    testProducts.forEach(product => {
      console.log(`- ${product.name} (SKU: ${product.sku})`);
    });

  } catch (error) {
    console.error('❌ Error adding test products:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestProducts();
