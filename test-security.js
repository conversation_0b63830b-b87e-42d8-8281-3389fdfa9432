// Test script for Phase 3 Enhanced Security Measures
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  // Test user credentials (cashier)
  email: '<EMAIL>',
  password: 'password123',
  
  // Test scenarios
  scenarios: {
    normalDiscrepancy: 50,    // Under threshold
    largeDiscrepancy: 150,    // Over $100 threshold
  }
};

class SecurityTester {
  constructor() {
    this.sessionToken = null;
    this.drawerSessionId = null;
  }

  async login() {
    console.log('🔐 Testing login and session creation...');
    
    const response = await fetch(`${BASE_URL}/api/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_CONFIG.email,
        password: TEST_CONFIG.password,
      }),
    });

    if (response.ok) {
      const data = await response.json();
      
      // Extract session token from Set-Cookie header
      const setCookieHeader = response.headers.get('set-cookie');
      if (setCookieHeader) {
        const tokenMatch = setCookieHeader.match(/session-token=([^;]+)/);
        if (tokenMatch) {
          this.sessionToken = tokenMatch[1];
          console.log('✅ Login successful, session token obtained');
          return true;
        }
      }
    }
    
    console.log('❌ Login failed');
    return false;
  }

  async testSecurityLogging() {
    console.log('\n🛡️ Testing security logging...');
    
    const response = await fetch(`${BASE_URL}/api/security/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session-token=${this.sessionToken}`,
      },
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Security logging test passed');
      console.log('   - Enhanced auth working');
      console.log('   - Security events logged');
      console.log('   - Session timeout created');
      return true;
    } else {
      console.log('❌ Security logging test failed:', data.error);
      return false;
    }
  }

  async getCurrentDrawerSession() {
    console.log('\n📦 Getting current drawer session...');
    
    const response = await fetch(`${BASE_URL}/api/drawer-sessions/current`, {
      headers: {
        'Cookie': `session-token=${this.sessionToken}`,
      },
    });

    if (response.ok) {
      const data = await response.json();
      if (data.session) {
        this.drawerSessionId = data.session.id;
        console.log('✅ Found active drawer session:', this.drawerSessionId);
        return data.session;
      }
    }
    
    console.log('❌ No active drawer session found');
    return null;
  }

  async testSessionTimeout() {
    console.log('\n⏰ Testing session timeout features...');
    
    // Test getting session timeout info
    const response = await fetch(`${BASE_URL}/api/session-timeout/drawer_session`, {
      headers: {
        'Cookie': `session-token=${this.sessionToken}`,
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Session timeout info retrieved');
      console.log(`   - Expires at: ${data.expiresAt}`);
      console.log(`   - Warning at: ${data.warningAt}`);
      return true;
    } else if (response.status === 404) {
      console.log('ℹ️ No active session timeout (expected for new sessions)');
      return true;
    } else {
      console.log('❌ Session timeout test failed');
      return false;
    }
  }

  async testDrawerCloseWithNormalDiscrepancy() {
    console.log('\n💰 Testing drawer close with normal discrepancy...');
    
    if (!this.drawerSessionId) {
      console.log('❌ No drawer session available for testing');
      return false;
    }

    const response = await fetch(`${BASE_URL}/api/drawer-sessions/${this.drawerSessionId}/close`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session-token=${this.sessionToken}`,
      },
      body: JSON.stringify({
        actualClosingBalance: 100 + TEST_CONFIG.scenarios.normalDiscrepancy, // Normal discrepancy
        notes: 'Test normal discrepancy - should not require re-auth',
      }),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Normal discrepancy drawer close successful');
      console.log(`   - Discrepancy: $${data.securityInfo?.threshold || 'N/A'}`);
      console.log(`   - Re-auth required: ${data.securityInfo?.reAuthRequired || false}`);
      return true;
    } else {
      console.log('❌ Normal discrepancy test failed:', data.error);
      return false;
    }
  }

  async testDrawerCloseWithLargeDiscrepancy() {
    console.log('\n🚨 Testing drawer close with large discrepancy...');
    
    if (!this.drawerSessionId) {
      console.log('❌ No drawer session available for testing');
      return false;
    }

    // First attempt without re-auth password (should fail)
    console.log('   Testing without re-auth password...');
    let response = await fetch(`${BASE_URL}/api/drawer-sessions/${this.drawerSessionId}/close`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session-token=${this.sessionToken}`,
      },
      body: JSON.stringify({
        actualClosingBalance: 100 + TEST_CONFIG.scenarios.largeDiscrepancy, // Large discrepancy
        notes: 'Test large discrepancy - should require re-auth',
      }),
    });

    let data = await response.json();
    
    if (response.status === 403 && data.requireReAuth) {
      console.log('✅ Large discrepancy correctly detected, re-auth required');
      console.log(`   - Discrepancy: $${data.discrepancy}`);
      console.log(`   - Threshold: $${data.threshold}`);
    } else {
      console.log('❌ Large discrepancy detection failed');
      return false;
    }

    // Second attempt with re-auth password (should succeed)
    console.log('   Testing with re-auth password...');
    response = await fetch(`${BASE_URL}/api/drawer-sessions/${this.drawerSessionId}/close`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `session-token=${this.sessionToken}`,
      },
      body: JSON.stringify({
        actualClosingBalance: 100 + TEST_CONFIG.scenarios.largeDiscrepancy,
        notes: 'Test large discrepancy with re-auth',
        reAuthPassword: TEST_CONFIG.password,
      }),
    });

    data = await response.json();
    
    if (response.ok) {
      console.log('✅ Large discrepancy with re-auth successful');
      console.log(`   - Security info: ${JSON.stringify(data.securityInfo)}`);
      return true;
    } else {
      console.log('❌ Large discrepancy with re-auth failed:', data.error);
      return false;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Phase 3 Enhanced Security Measures Testing\n');
    
    const results = {
      login: false,
      securityLogging: false,
      sessionTimeout: false,
      normalDiscrepancy: false,
      largeDiscrepancy: false,
    };

    // Test 1: Login and session creation
    results.login = await this.login();
    if (!results.login) {
      console.log('\n❌ Cannot proceed without valid session');
      return results;
    }

    // Test 2: Security logging
    results.securityLogging = await this.testSecurityLogging();

    // Test 3: Session timeout
    results.sessionTimeout = await this.testSessionTimeout();

    // Test 4: Get drawer session for testing
    const drawerSession = await this.getCurrentDrawerSession();

    // Test 5: Normal discrepancy (if drawer session available)
    if (drawerSession) {
      results.normalDiscrepancy = await this.testDrawerCloseWithNormalDiscrepancy();
    }

    // Test 6: Large discrepancy (if drawer session available)
    if (drawerSession) {
      results.largeDiscrepancy = await this.testDrawerCloseWithLargeDiscrepancy();
    }

    // Print summary
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    Object.entries(results).forEach(([test, passed]) => {
      console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All Phase 3 Enhanced Security Measures are working correctly!');
    } else {
      console.log('⚠️ Some tests failed. Please check the implementation.');
    }

    return results;
  }
}

// Run the tests
const tester = new SecurityTester();
tester.runAllTests().catch(console.error);
