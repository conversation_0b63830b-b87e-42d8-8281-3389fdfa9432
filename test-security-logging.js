#!/usr/bin/env node

/**
 * Test Security Logging Implementation
 * 
 * This script tests the enhanced security logging for authentication events
 * including failed login attempts, successful logins, and logout events.
 */

const BASE_URL = 'http://localhost:3000';

class SecurityLoggingTester {
  constructor() {
    this.sessionToken = null;
  }

  async testFailedLoginAttempts() {
    console.log('\n🔒 Testing Failed Login Attempts...');
    
    const failedAttempts = [
      {
        email: '<EMAIL>',
        password: 'wrongpassword',
        expectedReason: 'User not found'
      },
      {
        email: '<EMAIL>',
        password: 'wrongpassword',
        expectedReason: 'Incorrect password'
      },
      {
        email: 'invalid-email',
        password: 'password123',
        expectedReason: 'Validation failed'
      }
    ];

    for (const attempt of failedAttempts) {
      console.log(`\n   Testing: ${attempt.email} with wrong credentials`);
      
      try {
        const response = await fetch(`${BASE_URL}/api/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: attempt.email,
            password: attempt.password
          }),
        });

        const data = await response.json();
        
        if (!response.ok) {
          console.log(`   ✅ Failed as expected: ${data.error}`);
          console.log(`   📝 Should be logged as: ${attempt.expectedReason}`);
        } else {
          console.log(`   ❌ Unexpected success for invalid credentials`);
        }
      } catch (error) {
        console.log(`   ❌ Network error: ${error.message}`);
      }
      
      // Wait a bit between attempts
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  async testSuccessfulLogin() {
    console.log('\n✅ Testing Successful Login...');
    
    try {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        }),
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log(`   ✅ Login successful for: ${data.user.email}`);
        console.log(`   📝 Should be logged as: LOGIN_SUCCESS`);
        
        // Extract session token from cookies
        const cookies = response.headers.get('set-cookie');
        if (cookies) {
          const tokenMatch = cookies.match(/session-token=([^;]+)/);
          if (tokenMatch) {
            this.sessionToken = tokenMatch[1];
            console.log(`   🍪 Session token captured`);
          }
        }
        
        return true;
      } else {
        console.log(`   ❌ Login failed: ${data.error}`);
        return false;
      }
    } catch (error) {
      console.log(`   ❌ Network error: ${error.message}`);
      return false;
    }
  }

  async testLogout() {
    console.log('\n🚪 Testing Logout...');
    
    if (!this.sessionToken) {
      console.log('   ⚠️ No session token available, skipping logout test');
      return;
    }
    
    try {
      const response = await fetch(`${BASE_URL}/api/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `session-token=${this.sessionToken}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log(`   ✅ Logout successful`);
        console.log(`   📝 Should be logged as: LOGOUT_SUCCESS`);
        this.sessionToken = null;
      } else {
        console.log(`   ❌ Logout failed: ${data.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Network error: ${error.message}`);
    }
  }

  async checkSecurityLogs() {
    console.log('\n📊 Checking Security Logs...');
    
    // First login as admin to access security logs
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });

    if (!loginResponse.ok) {
      console.log('   ❌ Could not login to check security logs');
      return;
    }

    const loginData = await loginResponse.json();
    const cookies = loginResponse.headers.get('set-cookie');
    let sessionToken = null;
    
    if (cookies) {
      const tokenMatch = cookies.match(/session-token=([^;]+)/);
      if (tokenMatch) {
        sessionToken = tokenMatch[1];
      }
    }

    if (!sessionToken) {
      console.log('   ❌ Could not extract session token');
      return;
    }

    // Check security logs
    try {
      const logsResponse = await fetch(`${BASE_URL}/api/security/logs?limit=10`, {
        headers: {
          'Cookie': `session-token=${sessionToken}`,
        },
      });

      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        console.log(`   ✅ Security logs accessible`);
        console.log(`   📝 Found ${logsData.logs.length} recent security events`);
        
        // Show recent events
        const recentEvents = logsData.logs.slice(0, 5);
        console.log('\n   Recent Security Events:');
        recentEvents.forEach((event, index) => {
          console.log(`   ${index + 1}. ${event.action} - ${event.success ? 'SUCCESS' : 'FAILED'} - ${new Date(event.timestamp).toLocaleTimeString()}`);
          if (event.failureReason) {
            console.log(`      Reason: ${event.failureReason}`);
          }
        });
      } else {
        console.log(`   ❌ Could not access security logs: ${logsResponse.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Error checking security logs: ${error.message}`);
    }
  }

  async checkSecurityMetrics() {
    console.log('\n📈 Checking Security Metrics...');
    
    // Login as admin first
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });

    if (!loginResponse.ok) {
      console.log('   ❌ Could not login to check security metrics');
      return;
    }

    const cookies = loginResponse.headers.get('set-cookie');
    let sessionToken = null;
    
    if (cookies) {
      const tokenMatch = cookies.match(/session-token=([^;]+)/);
      if (tokenMatch) {
        sessionToken = tokenMatch[1];
      }
    }

    if (!sessionToken) {
      console.log('   ❌ Could not extract session token');
      return;
    }

    // Check security metrics
    try {
      const metricsResponse = await fetch(`${BASE_URL}/api/security/metrics`, {
        headers: {
          'Cookie': `session-token=${sessionToken}`,
        },
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        console.log(`   ✅ Security metrics accessible`);
        console.log(`   📊 Total Security Events (24h): ${metricsData.totalSecurityEvents}`);
        console.log(`   🚫 Failed Logins (24h): ${metricsData.failedLogins}`);
        console.log(`   ⚠️ Unauthorized Access (24h): ${metricsData.unauthorizedAccess}`);
        console.log(`   📱 Active Devices: ${metricsData.activeDevices}`);
        console.log(`   👥 Active Sessions: ${metricsData.activeSessions}`);
      } else {
        console.log(`   ❌ Could not access security metrics: ${metricsResponse.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Error checking security metrics: ${error.message}`);
    }
  }

  async runAllTests() {
    console.log('🛡️ Security Logging Test Suite');
    console.log('================================');
    
    // Test failed login attempts
    await this.testFailedLoginAttempts();
    
    // Test successful login
    const loginSuccess = await this.testSuccessfulLogin();
    
    // Test logout if login was successful
    if (loginSuccess) {
      await this.testLogout();
    }
    
    // Check if security events are being logged
    await this.checkSecurityLogs();
    
    // Check security metrics
    await this.checkSecurityMetrics();
    
    console.log('\n🎉 Security logging tests completed!');
    console.log('\n📝 Next Steps:');
    console.log('1. Check the Security Dashboard at: http://localhost:3000/admin/security');
    console.log('2. View Security Logs at: http://localhost:3000/admin/security/logs');
    console.log('3. Verify failed login attempts are visible in the dashboard');
    console.log('4. Check that metrics show the correct counts');
  }
}

// Run the tests
const tester = new SecurityLoggingTester();
tester.runAllTests().catch(console.error);
