import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addTestCashAuditData() {
  try {
    console.log('Adding test cash audit data...');

    // Get existing cash reconciliations
    const existingReconciliations = await prisma.cashReconciliation.findMany({
      include: {
        user: true
      }
    });

    console.log(`Found ${existingReconciliations.length} existing reconciliations`);

    // Update existing reconciliations with audit data
    for (const reconciliation of existingReconciliations) {
      const discrepancy = Number(reconciliation.discrepancy);
      
      // Add discrepancy category based on the amount
      let discrepancyCategory = null;
      if (discrepancy !== 0) {
        if (Math.abs(discrepancy) < 10000) {
          discrepancyCategory = 'COUNTING_ERROR';
        } else if (Math.abs(discrepancy) < 50000) {
          discrepancyCategory = discrepancy > 0 ? 'CASH_SURPLUS' : 'CASH_SHORTAGE';
        } else {
          discrepancyCategory = 'SYSTEM_ERROR';
        }
      }

      // Update the reconciliation
      await prisma.cashReconciliation.update({
        where: { id: reconciliation.id },
        data: {
          discrepancyCategory,
          resolutionStatus: Math.random() > 0.5 ? 'RESOLVED' : 'PENDING',
          resolutionNotes: discrepancyCategory ? 'Updated with audit data for testing' : null,
        }
      });

      // Create audit alert for large discrepancies
      if (Math.abs(discrepancy) > 50000) {
        await prisma.cashAuditAlert.create({
          data: {
            cashReconciliationId: reconciliation.id,
            alertType: discrepancy > 0 ? 'CASH_SURPLUS' : 'LARGE_DISCREPANCY',
            severity: Math.abs(discrepancy) > 100000 ? 'CRITICAL' : 'HIGH',
            threshold: 50000,
            actualValue: Math.abs(discrepancy),
            message: `Large ${discrepancy > 0 ? 'surplus' : 'shortage'} detected: Rp ${Math.abs(discrepancy).toLocaleString('id-ID')}`,
            isResolved: Math.random() > 0.5,
          }
        });
      }

      console.log(`Updated reconciliation ${reconciliation.id} with category: ${discrepancyCategory}`);
    }

    // Add some additional test reconciliations for the last few days
    const users = await prisma.user.findMany({
      where: { role: 'CASHIER', active: true }
    });

    if (users.length > 0) {
      const testData = [
        { days: 1, discrepancy: -25000, category: 'CASH_SHORTAGE' },
        { days: 2, discrepancy: 15000, category: 'COUNTING_ERROR' },
        { days: 3, discrepancy: -75000, category: 'THEFT_SUSPECTED' },
        { days: 4, discrepancy: 5000, category: 'REGISTER_ERROR' },
      ];

      for (const data of testData) {
        const businessDate = new Date();
        businessDate.setDate(businessDate.getDate() - data.days);
        businessDate.setHours(0, 0, 0, 0);

        const user = users[Math.floor(Math.random() * users.length)];
        const openingBalance = 100000;
        const expectedAmount = openingBalance + Math.random() * 500000;
        const actualAmount = expectedAmount + data.discrepancy;

        const reconciliation = await prisma.cashReconciliation.create({
          data: {
            businessDate,
            userId: user.id,
            openingBalance,
            expectedAmount,
            actualAmount,
            discrepancy: data.discrepancy,
            notes: `Test data: ${data.discrepancy > 0 ? 'surplus' : 'shortage'} of Rp ${Math.abs(data.discrepancy).toLocaleString('id-ID')}`,
            status: 'PENDING',
            discrepancyCategory: data.category,
            resolutionStatus: Math.random() > 0.5 ? 'RESOLVED' : 'PENDING',
            resolutionNotes: Math.random() > 0.5 ? 'Test resolution notes' : null,
          }
        });

        // Create audit alert for large discrepancies
        if (Math.abs(data.discrepancy) > 50000) {
          await prisma.cashAuditAlert.create({
            data: {
              cashReconciliationId: reconciliation.id,
              alertType: data.discrepancy > 0 ? 'CASH_SURPLUS' : 'LARGE_DISCREPANCY',
              severity: Math.abs(data.discrepancy) > 100000 ? 'CRITICAL' : 'HIGH',
              threshold: 50000,
              actualValue: Math.abs(data.discrepancy),
              message: `Large ${data.discrepancy > 0 ? 'surplus' : 'shortage'} detected: Rp ${Math.abs(data.discrepancy).toLocaleString('id-ID')}`,
              isResolved: Math.random() > 0.5,
            }
          });
        }

        console.log(`Created test reconciliation for ${businessDate.toDateString()} with discrepancy: Rp ${data.discrepancy.toLocaleString('id-ID')}`);
      }
    }

    console.log('Test cash audit data added successfully!');
  } catch (error) {
    console.error('Error adding test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestCashAuditData();
