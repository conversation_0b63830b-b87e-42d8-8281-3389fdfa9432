{"name": "npos", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:auth": "ts-node --esm src/lib/auth-test.ts", "seed": "prisma db seed"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@types/pdfkit": "^0.13.9", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "html2canvas": "^1.4.1", "html5-qrcode": "^2.3.8", "jsbarcode": "^3.11.6", "json2csv": "^6.0.0-alpha.2", "jspdf": "^3.0.1", "lucide-react": "^0.507.0", "next": "15.3.1", "next-auth": "^5.0.0-beta.27", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@faker-js/faker": "^9.7.0", "@tailwindcss/postcss": "^4.1.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20.17.50", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.5", "prisma": "^6.7.0", "tailwindcss": "^4", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.9", "typescript": "^5.8.3"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}