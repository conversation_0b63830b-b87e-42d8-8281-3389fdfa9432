// Test script to check device authorization status
const fetch = require('node-fetch');

async function testDeviceAuth() {
  try {
    // Test the device authorization API
    const response = await fetch('http://localhost:3000/api/auth/device-status', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add Jane's JWT token here - you'll need to get this from the browser
        'Cookie': 'auth-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.example'
      }
    });

    const data = await response.json();
    
    console.log('Device Authorization Status:');
    console.log('Status Code:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ API call successful');
      console.log('Authenticated:', data.authenticated);
      console.log('Device Authorized:', data.deviceAuthorized);
      console.log('User:', data.user?.email);
    } else {
      console.log('❌ API call failed');
      console.log('Error:', data.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDeviceAuth();
