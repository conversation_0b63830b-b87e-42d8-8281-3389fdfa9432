const calculateTargetProgress = (target: RevenueTarget, actualRevenue: number = 0) => {
  const now = new Date();
  const startDate = new Date(target.startDate);
  const endDate = new Date(target.endDate);
  
  // Calculate total days in the target period
  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  
  // Calculate days elapsed (how many days have passed since start)
  const daysElapsed = Math.max(0, Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1);
  
  // Calculate expected progress based on time elapsed
  const timeProgressPercentage = Math.min((daysElapsed / totalDays) * 100, 100);
  
  // Calculate actual revenue percentage
  const actualPercentage = target.amount > 0 ? Math.min((actualRevenue / target.amount) * 100, 100) : 0;
  
  // Calculate performance ratio (how we're doing vs expected)
  const expectedRevenue = (target.amount * timeProgressPercentage) / 100;
  const performanceRatio = expectedRevenue > 0 ? (actualRevenue / expectedRevenue) : 0;
  
  // Determine status based on performance ratio
  let status: "on-track" | "behind" | "critical" = "on-track";
  let color = "bg-green-500";
  
  if (performanceRatio < 0.7) { // Less than 70% of expected progress
    status = "critical";
    color = "bg-red-500";
  } else if (performanceRatio < 0.9) { // Less than 90% of expected progress
    status = "behind";
    color = "bg-yellow-500";
  }
  
  // Special case: if target period hasn't started yet
  if (now < startDate) {
    status = "on-track";
    color = "bg-blue-500";
  }
  
  // Special case: if target period has ended
  if (now > endDate) {
    if (actualPercentage >= 100) {
      status = "on-track";
      color = "bg-green-500";
    } else if (actualPercentage >= 80) {
      status = "behind";
      color = "bg-yellow-500";
    } else {
      status = "critical";
      color = "bg-red-500";
    }
  }

  return {
    percentage: Math.round(actualPercentage),
    status,
    color,
    actualRevenue,
    targetAmount: target.amount,
    difference: actualRevenue - target.amount,
    timeProgressPercentage: Math.round(timeProgressPercentage),
    expectedRevenue: Math.round(expectedRevenue),
    performanceRatio: Math.round(performanceRatio * 100) / 100,
    daysElapsed,
    totalDays,
  };
};
