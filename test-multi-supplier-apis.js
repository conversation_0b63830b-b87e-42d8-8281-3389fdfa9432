/**
 * Comprehensive API Testing Script for Phase 5.2 Multi-Supplier Implementation
 * Tests all 13 new API endpoints and 4 enhanced existing APIs
 */

const BASE_URL = 'http://localhost:3001';
let authToken = null;
let testData = {
  productId: null,
  supplierId: null,
  productSupplierId: null,
  stockBatchId: null,
  purchaseOrderId: null
};

// Helper function to make authenticated requests
async function makeRequest(method, endpoint, data = null, expectedStatus = 200) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': authToken ? `session-token=${authToken}` : ''
    }
  };

  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    options.body = JSON.stringify(data);
  }

  console.log(`\n🔄 ${method} ${endpoint}`);
  if (data) console.log('📤 Request:', JSON.stringify(data, null, 2));

  try {
    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let parsedData;
    try {
      parsedData = JSON.parse(responseData);
    } catch (e) {
      parsedData = responseData;
    }

    console.log(`📊 Status: ${response.status} (Expected: ${expectedStatus})`);
    
    if (response.status === expectedStatus) {
      console.log('✅ Success');
      if (parsedData && typeof parsedData === 'object') {
        console.log('📥 Response:', JSON.stringify(parsedData, null, 2));
      }
      return { success: true, data: parsedData, status: response.status };
    } else {
      console.log('❌ Failed');
      console.log('📥 Response:', parsedData);
      return { success: false, data: parsedData, status: response.status };
    }
  } catch (error) {
    console.log('💥 Error:', error.message);
    return { success: false, error: error.message };
  }
}

// Authentication function
async function authenticate() {
  console.log('\n🔐 Authenticating...');
  
  // First, try to get existing session
  const loginResponse = await fetch(`${BASE_URL}/api/auth/signin`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    })
  });

  if (loginResponse.ok) {
    const cookies = loginResponse.headers.get('set-cookie');
    if (cookies) {
      const tokenMatch = cookies.match(/session-token=([^;]+)/);
      if (tokenMatch) {
        authToken = tokenMatch[1];
        console.log('✅ Authentication successful');
        return true;
      }
    }
  }

  console.log('❌ Authentication failed');
  return false;
}

// Setup test data
async function setupTestData() {
  console.log('\n📋 Setting up test data...');

  // Get existing product and supplier
  const productsResponse = await makeRequest('GET', '/api/products?limit=1');
  if (productsResponse.success && productsResponse.data.products?.length > 0) {
    testData.productId = productsResponse.data.products[0].id;
    console.log(`✅ Using product ID: ${testData.productId}`);
  }

  const suppliersResponse = await makeRequest('GET', '/api/suppliers?limit=1');
  if (suppliersResponse.success && suppliersResponse.data.suppliers?.length > 0) {
    testData.supplierId = suppliersResponse.data.suppliers[0].id;
    console.log(`✅ Using supplier ID: ${testData.supplierId}`);
  }

  if (!testData.productId || !testData.supplierId) {
    console.log('❌ Failed to get test data');
    return false;
  }

  return true;
}

// Test 1: ProductSupplier Management APIs
async function testProductSupplierAPIs() {
  console.log('\n🧪 Testing ProductSupplier Management APIs...');

  // Test 1.1: GET /api/products/[id]/suppliers (empty initially)
  await makeRequest('GET', `/api/products/${testData.productId}/suppliers`);

  // Test 1.2: POST /api/products/[id]/suppliers (add supplier to product)
  const addSupplierResponse = await makeRequest('POST', `/api/products/${testData.productId}/suppliers`, {
    supplierId: testData.supplierId,
    supplierProductCode: 'TEST-SKU-001',
    supplierProductName: 'Test Product Name',
    purchasePrice: 25.99,
    minimumOrderQuantity: 10,
    leadTimeDays: 7,
    isPreferred: true,
    isActive: true,
    notes: 'Test supplier relationship'
  }, 201);

  if (addSupplierResponse.success) {
    testData.productSupplierId = addSupplierResponse.data.productSupplier?.id;
  }

  // Test 1.3: GET /api/products/[id]/suppliers (should now have one supplier)
  await makeRequest('GET', `/api/products/${testData.productId}/suppliers`);

  // Test 1.4: GET /api/products/[id]/suppliers/[supplierId] (get specific relationship)
  await makeRequest('GET', `/api/products/${testData.productId}/suppliers/${testData.supplierId}`);

  // Test 1.5: PUT /api/products/[id]/suppliers/[supplierId] (update relationship)
  await makeRequest('PUT', `/api/products/${testData.productId}/suppliers/${testData.supplierId}`, {
    purchasePrice: 24.99,
    notes: 'Updated test supplier relationship'
  });

  // Test 1.6: GET /api/suppliers/[id]/products (get products for supplier)
  await makeRequest('GET', `/api/suppliers/${testData.supplierId}/products`);

  console.log('✅ ProductSupplier Management APIs tested');
}

// Test 2: Preferred Supplier Management APIs
async function testPreferredSupplierAPIs() {
  console.log('\n🧪 Testing Preferred Supplier Management APIs...');

  // Test 2.1: GET /api/products/[id]/suppliers/preferred
  await makeRequest('GET', `/api/products/${testData.productId}/suppliers/preferred`);

  // Test 2.2: PUT /api/products/[id]/suppliers/preferred (set preferred)
  await makeRequest('PUT', `/api/products/${testData.productId}/suppliers/preferred`, {
    supplierId: testData.supplierId
  });

  // Test 2.3: GET /api/products/[id]/suppliers/preferred (verify preferred)
  await makeRequest('GET', `/api/products/${testData.productId}/suppliers/preferred`);

  console.log('✅ Preferred Supplier Management APIs tested');
}

// Test 3: StockBatch Management APIs
async function testStockBatchAPIs() {
  console.log('\n🧪 Testing StockBatch Management APIs...');

  // Test 3.1: GET /api/stock-batches (empty initially)
  await makeRequest('GET', '/api/stock-batches');

  // Test 3.2: POST /api/stock-batches (create manual batch)
  if (testData.productSupplierId) {
    const createBatchResponse = await makeRequest('POST', '/api/stock-batches', {
      productId: testData.productId,
      productSupplierId: testData.productSupplierId,
      batchNumber: 'TEST-BATCH-001',
      quantity: 100,
      purchasePrice: 24.99,
      location: 'warehouse',
      notes: 'Test batch creation'
    }, 201);

    if (createBatchResponse.success) {
      testData.stockBatchId = createBatchResponse.data.stockBatch?.id;
    }
  }

  // Test 3.3: GET /api/stock-batches (should now have batches)
  await makeRequest('GET', '/api/stock-batches');

  // Test 3.4: GET /api/stock-batches/[id] (get specific batch)
  if (testData.stockBatchId) {
    await makeRequest('GET', `/api/stock-batches/${testData.stockBatchId}`);

    // Test 3.5: PUT /api/stock-batches/[id] (update batch)
    await makeRequest('PUT', `/api/stock-batches/${testData.stockBatchId}`, {
      notes: 'Updated test batch'
    });
  }

  console.log('✅ StockBatch Management APIs tested');
}

// Test 4: Enhanced Product APIs
async function testEnhancedProductAPIs() {
  console.log('\n🧪 Testing Enhanced Product APIs...');

  // Test 4.1: GET /api/products (should include supplier info)
  await makeRequest('GET', '/api/products?limit=5');

  // Test 4.2: GET /api/products/[id] (should include suppliers and batches)
  await makeRequest('GET', `/api/products/${testData.productId}`);

  console.log('✅ Enhanced Product APIs tested');
}

// Test 5: Supplier Analytics APIs
async function testSupplierAnalyticsAPIs() {
  console.log('\n🧪 Testing Supplier Analytics APIs...');

  // Test 5.1: GET /api/analytics/suppliers (all suppliers)
  await makeRequest('GET', '/api/analytics/suppliers');

  // Test 5.2: GET /api/analytics/suppliers (specific supplier)
  await makeRequest('GET', `/api/analytics/suppliers?supplierId=${testData.supplierId}`);

  console.log('✅ Supplier Analytics APIs tested');
}

// Test 6: Error Handling and Validation
async function testErrorHandling() {
  console.log('\n🧪 Testing Error Handling and Validation...');

  // Test 6.1: Invalid product ID
  await makeRequest('GET', '/api/products/invalid-id/suppliers', null, 404);

  // Test 6.2: Invalid supplier data
  await makeRequest('POST', `/api/products/${testData.productId}/suppliers`, {
    supplierId: 'invalid-id',
    purchasePrice: -10 // Invalid negative price
  }, 400);

  // Test 6.3: Duplicate supplier relationship
  await makeRequest('POST', `/api/products/${testData.productId}/suppliers`, {
    supplierId: testData.supplierId,
    purchasePrice: 25.99
  }, 409);

  // Test 6.4: Unauthorized access (without proper role)
  // This would require testing with different user roles

  console.log('✅ Error Handling and Validation tested');
}

// Main test execution
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Multi-Supplier API Testing...');
  console.log('=' .repeat(60));

  // Step 1: Authenticate
  const authSuccess = await authenticate();
  if (!authSuccess) {
    console.log('💥 Cannot proceed without authentication');
    return;
  }

  // Step 2: Setup test data
  const setupSuccess = await setupTestData();
  if (!setupSuccess) {
    console.log('💥 Cannot proceed without test data');
    return;
  }

  // Step 3: Run all tests
  try {
    await testProductSupplierAPIs();
    await testPreferredSupplierAPIs();
    await testStockBatchAPIs();
    await testEnhancedProductAPIs();
    await testSupplierAnalyticsAPIs();
    await testErrorHandling();

    console.log('\n🎉 All tests completed!');
    console.log('=' .repeat(60));
    
    // Cleanup (optional)
    console.log('\n🧹 Cleaning up test data...');
    if (testData.stockBatchId) {
      await makeRequest('DELETE', `/api/stock-batches/${testData.stockBatchId}`);
    }
    if (testData.productId && testData.supplierId) {
      await makeRequest('DELETE', `/api/products/${testData.productId}/suppliers/${testData.supplierId}`);
    }

  } catch (error) {
    console.log('💥 Test execution failed:', error.message);
  }
}

// Run tests if this script is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, makeRequest, authenticate };
