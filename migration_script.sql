-- Migration script to add notification enhancements for Purchase Order approval system

-- Add new notification types to the enum
-- Note: This may need to be done manually depending on your database system

-- Add new columns to Notification table
ALTER TABLE "Notification" ADD COLUMN "purchaseOrderId" TEXT;
ALTER TABLE "Notification" ADD COLUMN "actionUrl" TEXT;
ALTER TABLE "Notification" ADD COLUMN "metadata" JSONB;

-- Add foreign key constraint for purchaseOrderId
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_purchaseOrderId_fkey" 
FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE CASCADE;

-- Update NotificationType enum to include new PO types
-- This may need to be done manually:
-- ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_APPROVAL';
-- ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_APPROVED';
-- ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_REJECTED';
-- ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_RECEIVED';
