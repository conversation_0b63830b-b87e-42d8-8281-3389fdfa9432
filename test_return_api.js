// Simple test to verify return creation API
console.log('🔍 Testing Return Creation API with new schema...\n');

// Test data for return creation
const testReturnData = {
  transactionId: "cmblp0ch80020cjc411vf00sv", // Use a known transaction ID
  customerId: null, // Test with null customer ID (walk-in customer)
  reason: "Test return - schema validation",
  notes: "Testing return creation after making customerId optional",
  items: [
    {
      productId: "cmbloxvfj001ccjc4t9m9r8dm",
      quantity: 1,
      unitPrice: 342000,
      subtotal: 342000
    }
  ]
};

console.log('Test data:');
console.log(JSON.stringify(testReturnData, null, 2));

console.log('\n✅ Test data prepared. This would be sent to POST /api/returns');
console.log('Expected behavior: Should succeed with null customerId');
console.log('Database schema now allows: customerId String? (optional)');
console.log('API validation allows: z.string().nullable()');
console.log('Prisma operation: customerId: validatedData.customerId (allows null)');
