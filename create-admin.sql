-- Create a super admin user with password 'admin123' (hashed)
INSERT INTO "User" (id, name, email, password, role, "createdAt", "updatedAt", active)
VALUES (
  'clu0admin1000000000000000001',
  'Super Admin',
  '<EMAIL>',
  '$2a$10$iqJSHD.BGr0E2IxQwYgJmeP3NvhPrXAeLSaGCj6IR/XU5QtjVu5Tm', -- hashed 'admin123'
  'SUPER_ADMIN',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP,
  true
)
ON CONFLICT (email) DO NOTHING;

-- Create a cashier user with password 'cashier123' (hashed)
INSERT INTO "User" (id, name, email, password, role, "createdAt", "updatedAt", active)
VALUES (
  'clu0cashier00000000000000001',
  'Cashier User',
  '<EMAIL>',
  '$2a$10$qnTGDEbYSKxYlAzQqNKA1eGe6UcxZ2ZAXSYkXmtZqFu9Wr9UKKjdq', -- hashed 'cashier123'
  'CASHIER',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP,
  true
)
ON CONFLICT (email) DO NOTHING;

-- Create a finance admin user with password 'finance123' (hashed)
INSERT INTO "User" (id, name, email, password, role, "createdAt", "updatedAt", active)
VALUES (
  'clu0finance00000000000000001',
  'Finance Admin',
  '<EMAIL>',
  '$2a$10$3JUkRYDJ.zlfWLBV7ODSxe.tCJvMtJU/xD7CVj5aDSVseT.RjJKwW', -- hashed 'finance123'
  'FINANCE_ADMIN',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP,
  true
)
ON CONFLICT (email) DO NOTHING;

-- Create a developer user with password 'developer123' (hashed)
INSERT INTO "User" (id, name, email, password, role, "createdAt", "updatedAt", active)
VALUES (
  'clu0developer000000000000001',
  'Developer',
  '<EMAIL>',
  '$2a$10$iqJSHD.BGr0E2IxQwYgJmeP3NvhPrXAeLSaGCj6IR/XU5QtjVu5Tm', -- hashed 'developer123'
  'DEVELOPER',
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP,
  true
)
ON CONFLICT (email) DO NOTHING;

-- Create a main cash drawer
INSERT INTO "CashDrawer" (id, name, location, "isActive", "createdAt", "updatedAt")
VALUES (
  'cld0main0000000000000000001',
  'Main Drawer',
  'Front Counter',
  true,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
)
ON CONFLICT (name) DO NOTHING;

-- Create basic system settings
INSERT INTO "SystemSetting" (key, value, description, "createdAt", "updatedAt")
VALUES 
  ('STORE_NAME', 'Next POS Store', 'Store name displayed in receipts and UI', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('STORE_ADDRESS', '123 Main St, City, Country', 'Store address displayed in receipts', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('STORE_PHONE', '****** 567 8900', 'Store phone number', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('CURRENCY_SYMBOL', '$', 'Currency symbol used throughout the application', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('TAX_RATE', '7.5', 'Default tax rate percentage', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('RECEIPT_FOOTER', 'Thank you for shopping with us!', 'Message displayed at the bottom of receipts', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('ENABLE_CHAT', 'true', 'Enable or disable the chat feature', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (key) DO UPDATE SET 
  value = EXCLUDED.value,
  "updatedAt" = CURRENT_TIMESTAMP;
