# Cart Quantity Increment Bug Fix - Test Plan

## Bug Description
**Issue**: When a user adds a product that already exists in the cart, the quantity was incorrectly increased by 2 instead of 1.

## Root Cause
The bug was caused by **duplicate event handlers** in the ProductSearch component:
1. `onMouseDown` event fired first and called `handleProductSelect(product)`
2. `onClick` event fired immediately after, causing the product to be added twice
3. The duplicate prevention logic wasn't robust enough to catch rapid successive calls

## Fix Applied

### 1. ProductSearch.tsx Changes
- **Removed duplicate event handlers**: Eliminated the `onMouseDown` handler
- **Consolidated to single `onClick` handler**: Now only uses `onClick` to handle product selection
- **Increased blur delay**: Extended the dropdown hide delay from 200ms to 300ms to ensure `onClick` fires properly

### 2. POSCartContext.tsx Changes
- **Extended duplicate prevention timeout**: Increased the timeout for clearing the `addToCartInProgress` flag from 100ms to 200ms
- **More robust duplicate prevention**: Better timing to prevent rapid successive calls

## Test Cases

### Test Case 1: Add New Product
**Steps**:
1. Navigate to POS page
2. Search for a product
3. Click on a product from the dropdown
4. Verify product is added with quantity = 1

**Expected Result**: Product appears in cart with quantity 1

### Test Case 2: Add Same Product Again (Main Bug Fix)
**Steps**:
1. Add a product to cart (quantity should be 1)
2. Search for the same product again
3. Click on the same product from the dropdown
4. Verify quantity increases by exactly 1

**Expected Result**: Product quantity increases from 1 to 2 (increment of +1, not +2)

### Test Case 3: Multiple Additions
**Steps**:
1. Add a product to cart (quantity = 1)
2. Add the same product again (quantity = 2)
3. Add the same product a third time (quantity = 3)
4. Verify each addition increases quantity by exactly 1

**Expected Result**: Quantity progression: 1 → 2 → 3

### Test Case 4: Different Products
**Steps**:
1. Add Product A to cart
2. Add Product B to cart
3. Add Product A again
4. Verify both products are in cart with correct quantities

**Expected Result**: 
- Product A: quantity = 2
- Product B: quantity = 1

### Test Case 5: Rapid Clicking Prevention
**Steps**:
1. Search for a product
2. Rapidly click on the same product multiple times in quick succession
3. Verify only one addition occurs

**Expected Result**: Product is added only once, not multiple times

## Manual Testing Instructions

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Navigate to POS page**: http://localhost:3000/pos

3. **Login with cashier credentials**:
   - Email: <EMAIL>
   - Password: cashier123

4. **Execute test cases above**

5. **Monitor browser console** for debug logs to verify the fix

## Verification Points

- ✅ Single click adds product once
- ✅ Adding existing product increases quantity by exactly 1
- ✅ No duplicate additions from rapid clicking
- ✅ Other cart operations (remove, update quantity) still work
- ✅ No console errors or warnings
- ✅ Toast notifications show correct messages

## Files Modified

1. **src/components/pos/ProductSearch.tsx**
   - Removed `onMouseDown` handler
   - Consolidated to single `onClick` handler
   - Increased blur delay to 300ms

2. **src/contexts/POSCartContext.tsx**
   - Extended duplicate prevention timeout to 200ms
   - Improved timing for clearing progress flags

## Additional Notes

- The fix maintains all existing functionality
- Performance is not impacted
- The duplicate prevention mechanism is now more robust
- Event handling is cleaner and more predictable
