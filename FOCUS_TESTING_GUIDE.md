# POS Focus Management Testing Guide

## Quick Testing Steps

### 1. Test Login Focus (Issue 1)
1. Open browser to `http://localhost:3000/login`
2. Login with cashier credentials (<EMAIL> / password)
3. **Expected**: After successful login and POS page loads, search field should automatically have focus
4. **Verification**: You should be able to immediately type without clicking
5. **Console**: Look for "POS: User authenticated and initialized, focusing search input"

### 2. Test Post-Payment Focus Recovery (Issue 2)
1. Add products to cart in POS
2. Complete a payment transaction
3. Receipt tab will open automatically
4. **Close the receipt tab** (click X or Ctrl+W)
5. **Expected**: When you return to POS tab, search field should automatically have focus
6. **Verification**: You should be able to immediately type/scan without clicking
7. **Console**: Look for "POS: Window regained focus, restoring search input focus"

### 3. Test Payment Dialog Focus Timing (Issue 3)
1. Add products to cart
2. Click "Proceed to Payment"
3. Fill payment details and click "Complete Payment"
4. **Expected**: Focus restoration should be triggered immediately when payment starts
5. **Console**: Look for "PaymentModal: Payment started, preparing focus restoration"
6. **Expected**: When receipt tab opens and you return, focus should already be ready
7. **Console**: Look for "PaymentModal: Receipt window opened, focus will be restored when user returns"

## Console Monitoring

Open browser developer tools (F12) and watch the console for these key messages:

### Successful Focus Events
- `POSContext: Focus successful` - Focus was applied successfully
- `POS: User authenticated and initialized, focusing search input` - Post-login focus
- `POS: Window regained focus, restoring search input focus` - Tab return focus
- `POS: Page became visible, restoring search input focus` - Visibility change focus

### Focus Preparation Events
- `PaymentModal: Payment started, preparing focus restoration` - Payment initiated
- `PaymentModal: Receipt window opened, focus will be restored when user returns` - Receipt tab opened

### Debug Information
- `POSContext: Focus attempt` with element state details
- `ProductSearch: Product selection completed, letting parent handle focus`

## Advanced Testing Scenarios

### Tab Switching Test
1. Complete a transaction (receipt tab opens)
2. Switch between POS tab and receipt tab multiple times
3. **Expected**: Each time you return to POS tab, focus should be restored
4. Try using Ctrl+Tab, clicking tabs, or Alt+Tab

### Multiple Transactions Test
1. Complete 3-4 transactions in sequence
2. For each transaction:
   - Add products
   - Process payment
   - Close receipt tab
   - Verify focus is ready for next transaction
3. **Expected**: Seamless workflow without manual clicking

### Error Scenario Test
1. Try with pop-up blocker enabled
2. **Expected**: Focus should still be restored even if receipt doesn't open
3. **Console**: Look for "PaymentModal: Pop-up blocked, restoring focus immediately"

## Troubleshooting

### If Focus Doesn't Work
1. Check browser console for error messages
2. Look for "POSContext: Focus error:" messages
3. Verify element state in console logs:
   - `isInDOM: true`
   - `isVisible: true` 
   - `isEnabled: true`

### If Focus is Delayed
1. Check console for retry messages: "POSContext: Element not ready for focus, retrying in 200ms"
2. This is normal during page transitions
3. Focus should succeed on retry

### If Focus Conflicts Occur
1. Look for multiple focus attempts in console
2. Check for "POSContext: Focus failed, retrying..." messages
3. The system should automatically resolve conflicts

## Expected Behavior Summary

✅ **Login** → Search field automatically focused  
✅ **Product selection** → Focus remains in search field  
✅ **Payment processing** → Focus prepared during payment  
✅ **Receipt tab opening** → Focus restoration triggered  
✅ **Receipt tab closing** → Focus immediately available  
✅ **Tab switching** → Focus restored on return  
✅ **Multiple transactions** → Seamless workflow  
✅ **Error scenarios** → Graceful focus recovery  

## Performance Notes

- Focus restoration uses optimized delays (100-500ms) to balance responsiveness and reliability
- Multiple fallback mechanisms ensure focus works even if primary method fails
- Event listeners are properly cleaned up to prevent memory leaks
- Retry logic handles edge cases where elements aren't immediately ready

## Browser Compatibility

Tested and working on:
- Chrome/Chromium
- Firefox
- Safari
- Edge

The implementation uses standard web APIs that work across all modern browsers.
