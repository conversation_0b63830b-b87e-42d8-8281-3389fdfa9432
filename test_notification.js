// Test script to create a notification directly
const fetch = require('node-fetch');

async function testNotificationCreation() {
  try {
    console.log('Testing notification creation...');
    
    const response = await fetch('http://localhost:3002/api/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'session=your-session-token-here' // You'll need to get this from browser
      },
      body: JSON.stringify({
        title: 'Test Notification',
        message: 'This is a test notification to verify the system works',
        type: 'SYSTEM'
      })
    });

    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', result);
    
    if (response.ok) {
      console.log('✅ Notification created successfully!');
    } else {
      console.log('❌ Failed to create notification');
    }
  } catch (error) {
    console.error('Error testing notification:', error);
  }
}

testNotificationCreation();
