// Debug script to check notification system
const { PrismaClient } = require('./src/generated/prisma');

const prisma = new PrismaClient();

async function debugNotificationSystem() {
  try {
    console.log('🔍 Debugging notification system...\n');

    // 1. Check users with approval roles
    console.log('1. Checking users with approval roles:');
    const approvalUsers = await prisma.user.findMany({
      where: {
        role: {
          in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN']
        },
        active: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        active: true,
      },
    });
    console.log(`Found ${approvalUsers.length} approval users:`);
    approvalUsers.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - ${user.role} - Active: ${user.active}`);
    });

    // 2. Check existing notifications
    console.log('\n2. Checking existing notifications:');
    const notifications = await prisma.notification.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        purchaseOrder: {
          select: {
            id: true,
            status: true,
          }
        }
      }
    });
    console.log(`Found ${notifications.length} notifications:`);
    notifications.forEach(notif => {
      console.log(`  - ${notif.title} (${notif.type}) - Read: ${notif.isRead} - PO: ${notif.purchaseOrderId || 'None'}`);
    });

    // 3. Check existing purchase orders
    console.log('\n3. Checking existing purchase orders:');
    const purchaseOrders = await prisma.purchaseOrder.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        status: true,
        isDraft: true,
        total: true,
        createdAt: true,
        supplier: {
          select: { name: true }
        },
        createdBy: {
          select: { name: true }
        }
      }
    });
    console.log(`Found ${purchaseOrders.length} purchase orders:`);
    purchaseOrders.forEach(po => {
      console.log(`  - ${po.id.slice(-8)} - ${po.status} - Draft: ${po.isDraft} - ${po.supplier.name} - ${po.total} - ${po.createdBy.name}`);
    });

    // 4. Check suppliers
    console.log('\n4. Checking suppliers:');
    const suppliers = await prisma.supplier.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        active: true,
      }
    });
    console.log(`Found ${suppliers.length} suppliers:`);
    suppliers.forEach(supplier => {
      console.log(`  - ${supplier.name} - Active: ${supplier.active}`);
    });

    // 5. Check products
    console.log('\n5. Checking products:');
    const products = await prisma.product.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        sku: true,
        active: true,
      }
    });
    console.log(`Found ${products.length} products:`);
    products.forEach(product => {
      console.log(`  - ${product.name} (${product.sku}) - Active: ${product.active}`);
    });

    console.log('\n✅ Debug complete!');

  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugNotificationSystem();
