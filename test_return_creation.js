// Test script to verify return creation API works
const fetch = require('node-fetch');

async function testReturnCreation() {
  try {
    console.log('Testing return creation API...');
    
    // First, let's get a valid transaction to create a return for
    const transactionsResponse = await fetch('http://localhost:3000/api/transactions?limit=1', {
      headers: {
        'Authorization': 'Bearer your-jwt-token-here', // You'll need to replace this
        'Content-Type': 'application/json',
      },
    });
    
    if (!transactionsResponse.ok) {
      console.log('Failed to fetch transactions:', transactionsResponse.status);
      return;
    }
    
    const transactionsData = await transactionsResponse.json();
    console.log('Transactions response:', JSON.stringify(transactionsData, null, 2));
    
    if (!transactionsData.transactions || transactionsData.transactions.length === 0) {
      console.log('No transactions found to create return for');
      return;
    }
    
    const transaction = transactionsData.transactions[0];
    console.log('Using transaction:', transaction.id);
    
    // Create a return for the first item in the transaction
    const returnData = {
      transactionId: transaction.id,
      customerId: transaction.customerId,
      reason: "Test return - defective product",
      notes: "Testing return creation API",
      items: transaction.items.slice(0, 1).map(item => ({
        productId: item.productId,
        quantity: 1, // Return 1 unit
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.unitPrice) * 1,
      })),
    };
    
    console.log('Creating return with data:', JSON.stringify(returnData, null, 2));
    
    const response = await fetch('http://localhost:3000/api/returns', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer your-jwt-token-here', // You'll need to replace this
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(returnData),
    });
    
    const responseText = await response.text();
    console.log('Response status:', response.status);
    console.log('Response text:', responseText);
    
    if (response.ok) {
      const result = JSON.parse(responseText);
      console.log('✅ Return created successfully!');
      console.log('Return ID:', result.id);
      console.log('Return status:', result.status);
    } else {
      console.log('❌ Failed to create return');
      try {
        const error = JSON.parse(responseText);
        console.log('Error details:', error);
      } catch (e) {
        console.log('Could not parse error response');
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Note: This is a basic test script. To run it properly, you would need:
// 1. A valid JWT token for authentication
// 2. Node.js with node-fetch installed
// 3. The development server running

console.log('This is a test script template.');
console.log('To use it, you need to:');
console.log('1. Replace "your-jwt-token-here" with a valid JWT token');
console.log('2. Install node-fetch: npm install node-fetch');
console.log('3. Run: node test_return_creation.js');
console.log('');
console.log('For now, please test manually through the web interface.');
