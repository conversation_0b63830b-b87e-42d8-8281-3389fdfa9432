# Database Backups Directory

This directory contains database backups for the Next POS application.

## Structure

- `.sql` files: Database dumps created by pg_dump
- `.sql.json` files: Metadata for each backup (timestamp, size, comments)

## Backup Rotation

By default, the system keeps the last 30 backups. Older backups are automatically deleted when new backups are created.

## Important

It's recommended to periodically copy these backups to external storage for additional safety.

For more information about the backup system, see `Docs/BACKUP_SYSTEM.md`.
