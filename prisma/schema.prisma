generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                         String                @id @default(cuid())
  name                       String
  email                      String                @unique
  password                   String?
  role                       UserRole              @default(CASHIER)
  createdAt                  DateTime              @default(now())
  updatedAt                  DateTime              @updatedAt
  active                     Boolean               @default(true)
  activityLogs               ActivityLog[]
  cashReconciliations        CashReconciliation[]
  investigatedReconciliations CashReconciliation[] @relation("CashReconciliationInvestigator")
  resolvedAuditAlerts        CashAuditAlert[]
  drawerSessions             DrawerSession[]
  receivedMessages           Message[]             @relation("ReceivedMessages")
  sentMessages               Message[]             @relation("SentMessages")
  receivedNotifications      Notification[]        @relation("UserNotifications")
  participantInConversations Participant[]
  approvedPOs                PurchaseOrder[]       @relation("ApprovedPurchaseOrders")
  purchaseOrders             PurchaseOrder[]       @relation("CreatedPurchaseOrders")
  cancelledPOs               PurchaseOrder[]       @relation("CancelledPurchaseOrders")
  sessions                   Session[]
  approvedSimpleTransfers    SimpleStockTransfer[] @relation("ApprovedSimpleTransfers")
  requestedSimpleTransfers   SimpleStockTransfer[] @relation("RequestedSimpleTransfers")
  starredConversations       StarredConversation[]
  stockAdjustments           StockAdjustment[]
  stockHistory               StockHistory[]
  temporaryPrices            TemporaryPrice[]
  revenueTargets             RevenueTarget[]
  approvals                  Transaction[]         @relation("ApproverTransactions")
  transactions               Transaction[]         @relation("CashierTransactions")
  cashDrawer                 CashDrawer?           @relation("UserDrawer")
  processedReturnResolutions Return[]              @relation("ReturnResolutionProcessor")
  receivedPurchaseOrders     PurchaseOrderReceiving[]
  createdPOTemplates         PurchaseOrderTemplate[] @relation("CreatedPOTemplates")

}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ActivityLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   String?
  timestamp DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Product {
  id                   String                @id @default(cuid())
  name                 String
  description          String?
  sku                  String                @unique
  barcode              String?               @unique
  categoryId           String?
  unitId               String
  basePrice            Decimal               @db.Decimal(10, 2)
  imageUrl             String?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  active               Boolean               @default(true)
  discountType         DiscountType?
  discountValue        Decimal?              @db.Decimal(10, 2)
  expiryDate           DateTime?
  optionalPrice1       Decimal?              @db.Decimal(10, 2)
  optionalPrice2       Decimal?              @db.Decimal(10, 2)
  purchasePrice        Decimal?              @db.Decimal(10, 2)
  supplierId           String?
  category             Category?             @relation(fields: [categoryId], references: [id])
  supplier             Supplier?             @relation(fields: [supplierId], references: [id])
  unit                 Unit                  @relation(fields: [unitId], references: [id])
  productSuppliers     ProductSupplier[]
  stockBatches         StockBatch[]
  purchaseOrderItems   PurchaseOrderItem[]
  returnItems          ReturnItem[]
  simpleStockTransfers SimpleStockTransfer[]
  stockAdjustments     StockAdjustment[]
  stockHistory         StockHistory[]
  storeStock           StoreStock?
  supplierReturnItems  SupplierReturnItem[]
  temporaryPrice       TemporaryPrice?
  transactionItems     TransactionItem[]
  warehouseStock       WarehouseStock?
  purchaseOrderTemplateItems PurchaseOrderTemplateItem[]
}

model ProductSupplier {
  id                    String    @id @default(cuid())
  productId             String
  supplierId            String
  supplierProductCode   String?   // Supplier's SKU/code for this product
  supplierProductName   String?   // Supplier's name for this product
  purchasePrice         Decimal   @db.Decimal(10, 2)
  minimumOrderQuantity  Decimal?  @db.Decimal(10, 2)
  leadTimeDays          Int?      // Lead time in days
  isPreferred           Boolean   @default(false)
  isActive              Boolean   @default(true)
  lastOrderDate         DateTime?
  lastPurchasePrice     Decimal?  @db.Decimal(10, 2)
  notes                 String?   // Supplier-specific notes
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relationships
  product               Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  supplier              Supplier  @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  purchaseOrderItems    PurchaseOrderItem[]
  stockBatches          StockBatch[]
  stockHistory          StockHistory[]

  // Constraints
  @@unique([productId, supplierId])
  @@index([productId])
  @@index([supplierId])
  @@index([isPreferred])
  @@index([isActive])
}

model StockBatch {
  id                    String         @id @default(cuid())
  productId             String
  productSupplierId     String         // Links to ProductSupplier
  batchNumber           String?        // Supplier batch number
  receivedDate          DateTime       @default(now())
  expiryDate            DateTime?
  quantity              Decimal        @db.Decimal(10, 2)
  remainingQuantity     Decimal        @db.Decimal(10, 2)
  purchasePrice         Decimal        @db.Decimal(10, 2)
  purchaseOrderId       String?        // Reference to originating PO
  warehouseStockId      String?        // Location tracking
  storeStockId          String?        // Location tracking
  status                BatchStatus    @default(ACTIVE)
  notes                 String?
  createdAt             DateTime       @default(now())
  updatedAt             DateTime       @updatedAt

  // Relationships
  product               Product        @relation(fields: [productId], references: [id], onDelete: Cascade)
  productSupplier       ProductSupplier @relation(fields: [productSupplierId], references: [id], onDelete: Cascade)
  purchaseOrder         PurchaseOrder? @relation(fields: [purchaseOrderId], references: [id])
  warehouseStock        WarehouseStock? @relation(fields: [warehouseStockId], references: [id])
  storeStock            StoreStock?    @relation(fields: [storeStockId], references: [id])
  stockHistory          StockHistory[]
  transactionItems      TransactionItem[]
  stockAdjustments      StockAdjustment[]

  @@index([productId])
  @@index([productSupplierId])
  @@index([status])
  @@index([expiryDate])
  @@index([receivedDate])
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Unit {
  id           String    @id @default(cuid())
  name         String    @unique
  abbreviation String
  description  String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  products     Product[]
}

model StoreStock {
  id               String            @id @default(cuid())
  productId        String            @unique
  quantity         Decimal           @db.Decimal(10, 2)
  minThreshold     Decimal           @db.Decimal(10, 2)
  lastUpdated      DateTime          @default(now())
  maxThreshold     Decimal?          @db.Decimal(10, 2)
  stockBatches     StockBatch[]
  stockAdjustments StockAdjustment[]
  stockHistory     StockHistory[]
  product          Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model WarehouseStock {
  id               String            @id @default(cuid())
  productId        String            @unique
  quantity         Decimal           @db.Decimal(10, 2)
  lastUpdated      DateTime          @default(now())
  maxThreshold     Decimal?          @db.Decimal(10, 2)
  minThreshold     Decimal           @default(0) @db.Decimal(10, 2)
  stockBatches     StockBatch[]
  stockAdjustments StockAdjustment[]
  stockHistory     StockHistory[]
  product          Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model StockAdjustment {
  id                 String           @id @default(cuid())
  date               DateTime         @default(now())
  productId          String
  batchId            String?          // NEW: Batch-level tracking
  storeStockId       String?
  warehouseStockId   String?
  previousQuantity   Decimal          @db.Decimal(10, 2)
  newQuantity        Decimal          @db.Decimal(10, 2)
  adjustmentQuantity Decimal          @db.Decimal(10, 2)
  reason             AdjustmentReason
  notes              String?
  userId             String
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  product            Product          @relation(fields: [productId], references: [id])
  stockBatch         StockBatch?      @relation(fields: [batchId], references: [id])
  storeStock         StoreStock?      @relation(fields: [storeStockId], references: [id])
  user               User             @relation(fields: [userId], references: [id])
  warehouseStock     WarehouseStock?  @relation(fields: [warehouseStockId], references: [id])
}

model StockHistory {
  id                String            @id @default(cuid())
  date              DateTime          @default(now())
  productId         String
  productSupplierId String?           // NEW: Supplier tracking
  batchId           String?           // NEW: Batch-level tracking
  storeStockId      String?
  warehouseStockId  String?
  previousQuantity  Decimal           @db.Decimal(10, 2)
  newQuantity       Decimal           @db.Decimal(10, 2)
  changeQuantity    Decimal           @db.Decimal(10, 2)
  source            StockChangeSource
  referenceId       String?
  referenceType     String?
  notes             String?
  userId            String
  createdAt         DateTime          @default(now())
  product           Product           @relation(fields: [productId], references: [id])
  productSupplier   ProductSupplier?  @relation(fields: [productSupplierId], references: [id])
  stockBatch        StockBatch?       @relation(fields: [batchId], references: [id])
  storeStock        StoreStock?       @relation(fields: [storeStockId], references: [id])
  user              User              @relation(fields: [userId], references: [id])
  warehouseStock    WarehouseStock?   @relation(fields: [warehouseStockId], references: [id])
}

model SimpleStockTransfer {
  id            String         @id @default(cuid())
  date          DateTime       @default(now())
  productId     String
  quantity      Decimal        @db.Decimal(10, 2)
  fromStore     Boolean
  toStore       Boolean
  status        TransferStatus @default(PENDING)
  notes         String?
  requestedById String
  approvedById  String?
  approvedAt    DateTime?
  completedAt   DateTime?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  approvedBy    User?          @relation("ApprovedSimpleTransfers", fields: [approvedById], references: [id])
  product       Product        @relation(fields: [productId], references: [id])
  requestedBy   User           @relation("RequestedSimpleTransfers", fields: [requestedById], references: [id])
}

model Transaction {
  id              String            @id @default(cuid())
  transactionDate DateTime          @default(now())
  cashierId       String
  customerId      String?
  subtotal        Decimal           @db.Decimal(10, 2)
  discount        Decimal           @default(0) @db.Decimal(10, 2)
  tax             Decimal           @default(0) @db.Decimal(10, 2)
  total           Decimal           @db.Decimal(10, 2)
  paymentMethod   PaymentMethod
  paymentStatus   PaymentStatus     @default(PENDING)
  cashReceived    Decimal?          @db.Decimal(10, 2)
  changeAmount    Decimal?          @db.Decimal(10, 2)
  dueDate         DateTime?
  approverId      String?
  approvedAt      DateTime?
  status          TransactionStatus @default(PENDING)
  notes           String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  drawerSessionId String?
  terminalId      String?
  returns         Return[]
  approver        User?             @relation("ApproverTransactions", fields: [approverId], references: [id])
  cashier         User              @relation("CashierTransactions", fields: [cashierId], references: [id])
  customer        Customer?         @relation(fields: [customerId], references: [id])
  drawerSession   DrawerSession?    @relation(fields: [drawerSessionId], references: [id])
  terminal        Terminal?         @relation(fields: [terminalId], references: [id])
  items           TransactionItem[]
}

model TransactionItem {
  id            String       @id @default(cuid())
  transactionId String
  productId     String
  batchId       String?      // NEW: Batch tracking for FIFO/LIFO
  quantity      Decimal      @db.Decimal(10, 2)
  unitPrice     Decimal      @db.Decimal(10, 2)
  discount      Decimal      @default(0) @db.Decimal(10, 2)
  subtotal      Decimal      @db.Decimal(10, 2)
  product       Product      @relation(fields: [productId], references: [id])
  stockBatch    StockBatch?  @relation(fields: [batchId], references: [id])
  transaction   Transaction  @relation(fields: [transactionId], references: [id], onDelete: Cascade)
}

model Customer {
  id           String        @id @default(cuid())
  name         String
  phone        String?
  email        String?
  address      String?
  customerType CustomerType  @default(REGULAR)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  returns      Return[]
  transactions Transaction[]
}

model PurchaseOrder {
  id              String              @id @default(cuid())
  orderDate       DateTime            @default(now())
  createdById     String
  supplierId      String
  subtotal        Decimal             @db.Decimal(10, 2)
  tax             Decimal             @default(0) @db.Decimal(10, 2)
  taxPercentage   Decimal?            @db.Decimal(5, 2)
  total           Decimal             @db.Decimal(10, 2)
  status          POStatus            @default(DRAFT)
  approvedById    String?
  approvedAt      DateTime?
  orderedAt       DateTime?           // NEW: When PO was sent to supplier
  shippedAt       DateTime?           // NEW: When supplier shipped the order
  expectedDeliveryDate DateTime?      // NEW: Expected delivery date
  receivedAt      DateTime?
  cancelledAt     DateTime?           // NEW: When PO was cancelled
  cancelledById   String?             // NEW: Who cancelled the PO
  cancelReason    String?             // NEW: Reason for cancellation
  priority        String?             @default("NORMAL") // NEW: Priority level (LOW, NORMAL, HIGH, URGENT)
  notes           String?
  approvalNotes   String?             // NEW: Notes from approval process
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  approvedBy      User?               @relation("ApprovedPurchaseOrders", fields: [approvedById], references: [id])
  createdBy       User                @relation("CreatedPurchaseOrders", fields: [createdById], references: [id])
  cancelledBy     User?               @relation("CancelledPurchaseOrders", fields: [cancelledById], references: [id])
  supplier        Supplier            @relation(fields: [supplierId], references: [id])
  stockBatches    StockBatch[]
  items           PurchaseOrderItem[]
  receivings      PurchaseOrderReceiving[]
  supplierReturns SupplierReturn[]
  notifications   Notification[]      @relation("PurchaseOrderNotifications")
}

model PurchaseOrderItem {
  id                    String          @id @default(cuid())
  purchaseOrderId       String
  productId             String
  productSupplierId     String?         // NEW: Link to ProductSupplier
  supplierProductCode   String?         // NEW: Supplier's product code at order time
  quantity              Decimal         @db.Decimal(10, 2)
  receivedQuantity      Decimal         @default(0) @db.Decimal(10, 2)
  unitPrice             Decimal         @db.Decimal(10, 2)
  subtotal              Decimal         @db.Decimal(10, 2)
  product               Product         @relation(fields: [productId], references: [id])
  productSupplier       ProductSupplier? @relation(fields: [productSupplierId], references: [id])
  purchaseOrder         PurchaseOrder   @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)

  @@index([productSupplierId])
}

model PurchaseOrderReceiving {
  id              String        @id @default(cuid())
  purchaseOrderId String
  receivedById    String
  receivedAt      DateTime      @default(now())
  notes           String?
  discrepancyReason String?
  items           PurchaseOrderReceivingItem[]
  purchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  receivedBy      User          @relation(fields: [receivedById], references: [id])
  createdAt       DateTime      @default(now())
}

model PurchaseOrderReceivingItem {
  id                        String                  @id @default(cuid())
  purchaseOrderReceivingId  String
  purchaseOrderItemId       String
  receivedQuantity          Decimal                 @db.Decimal(10, 2)
  discrepancyQuantity       Decimal                 @default(0) @db.Decimal(10, 2)
  discrepancyReason         String?
  notes                     String?
  receiving                 PurchaseOrderReceiving  @relation(fields: [purchaseOrderReceivingId], references: [id], onDelete: Cascade)
}

model PurchaseOrderTemplate {
  id              String                      @id @default(cuid())
  name            String
  description     String?
  supplierId      String
  taxPercentage   Decimal?                    @db.Decimal(5, 2)
  notes           String?
  isActive        Boolean                     @default(true)
  createdById     String
  createdAt       DateTime                    @default(now())
  updatedAt       DateTime                    @updatedAt
  supplier        Supplier                    @relation(fields: [supplierId], references: [id])
  createdBy       User                        @relation("CreatedPOTemplates", fields: [createdById], references: [id])
  items           PurchaseOrderTemplateItem[]
}

model PurchaseOrderTemplateItem {
  id                      String                @id @default(cuid())
  purchaseOrderTemplateId String
  productId               String
  quantity                Decimal               @db.Decimal(10, 2)
  unitPrice               Decimal               @db.Decimal(10, 2)
  template                PurchaseOrderTemplate @relation(fields: [purchaseOrderTemplateId], references: [id], onDelete: Cascade)
  product                 Product               @relation(fields: [productId], references: [id])
}

model Supplier {
  id                     String                  @id @default(cuid())
  name                   String
  contactPerson          String?
  phone                  String?
  email                  String?
  address                String?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  products               Product[]
  productSuppliers       ProductSupplier[]       // NEW: Multiple products relationship
  purchaseOrders         PurchaseOrder[]
  returns                SupplierReturn[]
  purchaseOrderTemplates PurchaseOrderTemplate[]
}

model Return {
  id                         String                @id @default(cuid())
  returnDate                 DateTime              @default(now())
  transactionId              String
  customerId                 String?
  reason                     String
  total                      Decimal               @db.Decimal(10, 2)
  status                     ReturnStatus          @default(PENDING)
  disposition                ReturnDisposition?
  dispositionReason          String?
  addToSupplierQueue         Boolean               @default(false)
  supplierReturnQueueId      String?
  customerResolution         CustomerResolution?
  customerResolutionNotes    String?
  customerResolutionProcessedAt DateTime?
  customerResolutionProcessedBy String?
  awaitingRestock            Boolean               @default(false)
  notes                      String?
  createdAt                  DateTime              @default(now())
  updatedAt                  DateTime              @updatedAt
  customer                   Customer?             @relation(fields: [customerId], references: [id])
  transaction                Transaction           @relation(fields: [transactionId], references: [id])
  items                      ReturnItem[]
  supplierReturnQueue        SupplierReturn?       @relation(fields: [supplierReturnQueueId], references: [id])
  customerResolutionProcessor User?                @relation("ReturnResolutionProcessor", fields: [customerResolutionProcessedBy], references: [id])
}

model ReturnItem {
  id        String  @id @default(cuid())
  returnId  String
  productId String
  quantity  Decimal @db.Decimal(10, 2)
  unitPrice Decimal @db.Decimal(10, 2)
  subtotal  Decimal @db.Decimal(10, 2)
  return    Return  @relation(fields: [returnId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])
}

model SupplierReturn {
  id              String               @id @default(cuid())
  returnDate      DateTime             @default(now())
  purchaseOrderId String?
  supplierId      String
  reason          String
  total           Decimal              @db.Decimal(10, 2)
  status          ReturnStatus         @default(PENDING)
  notes           String?
  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt
  purchaseOrder   PurchaseOrder?       @relation(fields: [purchaseOrderId], references: [id])
  supplier        Supplier             @relation(fields: [supplierId], references: [id])
  items           SupplierReturnItem[]
  customerReturns Return[]
}

model SupplierReturnItem {
  id               String         @id @default(cuid())
  supplierReturnId String
  productId        String
  quantity         Decimal        @db.Decimal(10, 2)
  unitPrice        Decimal        @db.Decimal(10, 2)
  subtotal         Decimal        @db.Decimal(10, 2)
  supplierReturn   SupplierReturn @relation(fields: [supplierReturnId], references: [id], onDelete: Cascade)
  product          Product        @relation(fields: [productId], references: [id])
}

model Notification {
  id               String           @id @default(cuid())
  userId           String
  title            String
  message          String
  type             NotificationType @default(SYSTEM)
  isRead           Boolean          @default(false)
  createdAt        DateTime         @default(now())
  // Purchase Order specific fields
  purchaseOrderId  String?
  actionUrl        String?          // Direct link to relevant page
  metadata         Json?            // Additional data (supplier name, amount, etc.)
  user             User             @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)
  purchaseOrder    PurchaseOrder?   @relation("PurchaseOrderNotifications", fields: [purchaseOrderId], references: [id], onDelete: Cascade)
}

model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model StoreInfo {
  id        String   @id @default("default-store")
  storeName String
  phone     String?
  address   String?
  email     String?
  website   String?
  taxId     String?
  logoUrl   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Conversation {
  id           String                @id @default(cuid())
  title        String?
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  lastActivity DateTime              @default(now())
  messages     Message[]
  participants Participant[]
  starredBy    StarredConversation[]
}

model StarredConversation {
  id             String       @id @default(cuid())
  userId         String
  conversationId String
  starredAt      DateTime     @default(now())
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, conversationId])
}

model Participant {
  id             String       @id @default(cuid())
  userId         String
  conversationId String
  joinedAt       DateTime     @default(now())
  leftAt         DateTime?
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, conversationId])
}

model Message {
  id             String       @id @default(cuid())
  conversationId String
  senderId       String
  receiverId     String?
  content        String
  isRead         Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  receiver       User?        @relation("ReceivedMessages", fields: [receiverId], references: [id])
  sender         User         @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
}

model CashDrawer {
  id             String          @id @default(cuid())
  name           String
  location       String?
  isActive       Boolean         @default(true)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  userId         String?         @unique
  drawerSessions DrawerSession[]
  terminal       Terminal?       @relation("DrawerTerminal")
  user           User?           @relation("UserDrawer", fields: [userId], references: [id])
}

model Terminal {
  id             String          @id @default(cuid())
  name           String
  ipAddress      String?
  macAddress     String?
  location       String?
  description    String?
  isActive       Boolean         @default(true)
  drawerId       String?         @unique
  drawer         CashDrawer?     @relation("DrawerTerminal", fields: [drawerId], references: [id])
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  transactions   Transaction[]
  drawerSessions DrawerSession[]
}

model DrawerSession {
  id                     String              @id @default(cuid())
  drawerId               String
  userId                 String
  terminalId             String              // Made required - every session must have a terminal
  businessDate           DateTime            @db.Date
  openingBalance         Decimal             @db.Decimal(10, 2)
  expectedClosingBalance Decimal?            @db.Decimal(10, 2)
  actualClosingBalance   Decimal?            @db.Decimal(10, 2)
  discrepancy            Decimal?            @db.Decimal(10, 2)
  openedAt               DateTime            @default(now())
  closedAt               DateTime?
  status                 DrawerSessionStatus @default(OPEN)
  notes                  String?
  shiftNumber            Int?                // Track shift number for the day
  previousSessionId      String?             @unique // Link to previous session for same drawer/terminal
  drawer                 CashDrawer          @relation(fields: [drawerId], references: [id])
  terminal               Terminal            @relation(fields: [terminalId], references: [id])
  user                   User                @relation(fields: [userId], references: [id])
  transactions           Transaction[]
  previousSession        DrawerSession?      @relation("SessionChain", fields: [previousSessionId], references: [id])
  nextSession            DrawerSession?      @relation("SessionChain")

  // Note: Unique constraints for OPEN sessions only are handled by partial indexes in migration
  // This allows multiple CLOSED sessions but only one OPEN session per user/terminal
}

model CashReconciliation {
  id                  String                    @id @default(cuid())
  businessDate        DateTime                  @db.Date
  userId              String
  openingBalance      Decimal                   @db.Decimal(10, 2)
  expectedAmount      Decimal                   @db.Decimal(10, 2)
  actualAmount        Decimal                   @db.Decimal(10, 2)
  discrepancy         Decimal                   @db.Decimal(10, 2)
  notes               String?
  status              ReconciliationStatus      @default(PENDING)
  discrepancyCategory DiscrepancyCategory?
  resolutionStatus    ResolutionStatus          @default(PENDING)
  resolutionNotes     String?
  investigatedBy      String?
  resolvedAt          DateTime?
  createdAt           DateTime                  @default(now())
  updatedAt           DateTime                  @updatedAt
  user                User                      @relation(fields: [userId], references: [id])
  investigator        User?                     @relation("CashReconciliationInvestigator", fields: [investigatedBy], references: [id])
  auditAlerts         CashAuditAlert[]
}

model CashAuditAlert {
  id                     String              @id @default(cuid())
  cashReconciliationId   String
  alertType              AuditAlertType
  severity               AlertSeverity       @default(MEDIUM)
  message                String
  threshold              Decimal?            @db.Decimal(10, 2)
  actualValue            Decimal?            @db.Decimal(10, 2)
  isResolved             Boolean             @default(false)
  resolvedBy             String?
  resolvedAt             DateTime?
  createdAt              DateTime            @default(now())
  updatedAt              DateTime            @updatedAt
  cashReconciliation     CashReconciliation  @relation(fields: [cashReconciliationId], references: [id], onDelete: Cascade)
  resolver               User?               @relation(fields: [resolvedBy], references: [id])
}

model TemporaryPrice {
  id        String       @id @default(cuid())
  productId String       @unique
  value     Decimal      @db.Decimal(10, 2)
  type      DiscountType
  startDate DateTime
  endDate   DateTime
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  createdBy String
  user      User         @relation(fields: [createdBy], references: [id])
  product   Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model RevenueTarget {
  id          String            @id @default(cuid())
  name        String            // e.g., "Q1 2025 Target", "January 2025"
  description String?
  targetType  RevenueTargetType @default(MONTHLY)
  startDate   DateTime          @db.Date
  endDate     DateTime          @db.Date
  amount      Decimal           @db.Decimal(12, 2)
  isActive    Boolean           @default(true)
  createdBy   String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  user        User              @relation(fields: [createdBy], references: [id])

  @@unique([startDate, endDate, targetType])
}

enum UserRole {
  SUPER_ADMIN
  CASHIER
  FINANCE_ADMIN
  WAREHOUSE_ADMIN
  MARKETING
  DEVELOPER
}

enum CustomerType {
  REGULAR
  FRIEND
  FAMILY
}

enum PaymentMethod {
  CASH
  DEBIT
  QRIS
}

enum PaymentStatus {
  PENDING
  PAID
  PARTIAL
  OVERDUE
  CANCELLED
}

enum TransactionStatus {
  PENDING
  COMPLETED
  VOIDED
  RETURNED
}

enum DrawerSessionStatus {
  OPEN
  CLOSED
  RECONCILED
}

enum ReconciliationStatus {
  PENDING
  COMPLETED
  APPROVED
  REJECTED
}

enum POStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  ORDERED
  SHIPPED
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
  OVERDUE
}

enum ReturnStatus {
  PENDING
  APPROVED
  COMPLETED
  REJECTED
}

enum ReturnDisposition {
  RETURN_TO_STOCK
  DO_NOT_RETURN_TO_STOCK
}

enum CustomerResolution {
  REPLACEMENT
  REFUND
  PENDING_REPLACEMENT
  NONE
}

enum NotificationType {
  SYSTEM
  MESSAGE
  ALERT
  INFO
  PURCHASE_ORDER_APPROVAL
  PURCHASE_ORDER_APPROVED
  PURCHASE_ORDER_REJECTED
  PURCHASE_ORDER_RECEIVED
}

enum DiscountType {
  FIXED
  PERCENTAGE
}

enum AdjustmentReason {
  INVENTORY_COUNT
  DAMAGED
  EXPIRED
  THEFT
  LOSS
  RETURN
  CORRECTION
  OTHER
}

enum StockChangeSource {
  PURCHASE
  SALE
  ADJUSTMENT
  TRANSFER
  RETURN
  INITIAL
  OTHER
}

enum StockLocationType {
  STORE
  WAREHOUSE
}

enum RevenueTargetType {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum TransferStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
  CANCELLED
}

enum DiscrepancyCategory {
  COUNTING_ERROR
  SYSTEM_ERROR
  THEFT_SUSPECTED
  CASH_SHORTAGE
  CASH_SURPLUS
  REGISTER_ERROR
  TRAINING_ERROR
  PROCEDURAL_ERROR
  UNKNOWN
  OTHER
}

enum ResolutionStatus {
  PENDING
  INVESTIGATING
  RESOLVED
  WRITTEN_OFF
  ESCALATED
  CLOSED
}

enum AuditAlertType {
  LARGE_DISCREPANCY
  FREQUENT_SHORTAGES
  PATTERN_DETECTED
  THRESHOLD_EXCEEDED
  UNUSUAL_ACTIVITY
  COMPLIANCE_VIOLATION
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum BatchStatus {
  ACTIVE
  EXPIRED
  RECALLED
  SOLD_OUT
}

