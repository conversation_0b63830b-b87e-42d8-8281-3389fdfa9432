/*
  Warnings:

  - A unique constraint covering the columns `[previousSessionId]` on the table `DrawerSession` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,status]` on the table `DrawerSession` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[terminalId,status]` on the table `DrawerSession` will be added. If there are existing duplicate values, this will fail.
  - Made the column `terminalId` on table `DrawerSession` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "DrawerSession" DROP CONSTRAINT "DrawerSession_terminalId_fkey";

-- AlterTable
ALTER TABLE "DrawerSession" ADD COLUMN     "previousSessionId" TEXT,
ADD COLUMN     "shiftNumber" INTEGER;

-- Update existing NULL terminalId values with the first available terminal
-- First, get the first terminal ID and update all NULL terminalId records
UPDATE "DrawerSession"
SET "terminalId" = (
  SELECT "id"
  FROM "Terminal"
  WHERE "isActive" = true
  LIMIT 1
)
WHERE "terminalId" IS NULL;

-- Now make the column NOT NULL
ALTER TABLE "DrawerSession" ALTER COLUMN "terminalId" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "DrawerSession_previousSessionId_key" ON "DrawerSession"("previousSessionId");

-- CreateIndex
CREATE UNIQUE INDEX "DrawerSession_userId_status_key" ON "DrawerSession"("userId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "DrawerSession_terminalId_status_key" ON "DrawerSession"("terminalId", "status");

-- AddForeignKey
ALTER TABLE "DrawerSession" ADD CONSTRAINT "DrawerSession_terminalId_fkey" FOREIGN KEY ("terminalId") REFERENCES "Terminal"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DrawerSession" ADD CONSTRAINT "DrawerSession_previousSessionId_fkey" FOREIGN KEY ("previousSessionId") REFERENCES "DrawerSession"("id") ON DELETE SET NULL ON UPDATE CASCADE;
