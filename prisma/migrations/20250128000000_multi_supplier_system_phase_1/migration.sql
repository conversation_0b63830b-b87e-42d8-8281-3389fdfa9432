-- Migration for Multi-Supplier Product Management System - Phase 1
-- This migration creates the new tables and relationships for multi-supplier support

-- Create BatchStatus enum
CREATE TYPE "BatchStatus" AS ENUM ('ACTIVE', 'EXPIRED', 'RECALLED', 'SOLD_OUT');

-- Create ProductSupplier junction table
CREATE TABLE "ProductSupplier" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "supplierId" TEXT NOT NULL,
    "supplierProductCode" TEXT,
    "supplierProductName" TEXT,
    "purchasePrice" DECIMAL(10,2) NOT NULL,
    "minimumOrderQuantity" DECIMAL(10,2),
    "leadTimeDays" INTEGER,
    "isPreferred" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastOrderDate" TIMESTAMP(3),
    "lastPurchasePrice" DECIMAL(10,2),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductSupplier_pkey" PRIMARY KEY ("id")
);

-- Create StockBatch table for enhanced tracking
CREATE TABLE "StockBatch" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "productSupplierId" TEXT NOT NULL,
    "batchNumber" TEXT,
    "receivedDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiryDate" TIMESTAMP(3),
    "quantity" DECIMAL(10,2) NOT NULL,
    "remainingQuantity" DECIMAL(10,2) NOT NULL,
    "purchasePrice" DECIMAL(10,2) NOT NULL,
    "purchaseOrderId" TEXT,
    "warehouseStockId" TEXT,
    "storeStockId" TEXT,
    "status" "BatchStatus" NOT NULL DEFAULT 'ACTIVE',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StockBatch_pkey" PRIMARY KEY ("id")
);

-- Add new columns to existing tables
ALTER TABLE "PurchaseOrderItem" ADD COLUMN "productSupplierId" TEXT;
ALTER TABLE "PurchaseOrderItem" ADD COLUMN "supplierProductCode" TEXT;

ALTER TABLE "StockHistory" ADD COLUMN "productSupplierId" TEXT;
ALTER TABLE "StockHistory" ADD COLUMN "batchId" TEXT;

ALTER TABLE "StockAdjustment" ADD COLUMN "batchId" TEXT;

ALTER TABLE "TransactionItem" ADD COLUMN "batchId" TEXT;

-- Create unique constraints and indexes
CREATE UNIQUE INDEX "ProductSupplier_productId_supplierId_key" ON "ProductSupplier"("productId", "supplierId");
CREATE INDEX "ProductSupplier_productId_idx" ON "ProductSupplier"("productId");
CREATE INDEX "ProductSupplier_supplierId_idx" ON "ProductSupplier"("supplierId");
CREATE INDEX "ProductSupplier_isPreferred_idx" ON "ProductSupplier"("isPreferred");
CREATE INDEX "ProductSupplier_isActive_idx" ON "ProductSupplier"("isActive");

CREATE INDEX "StockBatch_productId_idx" ON "StockBatch"("productId");
CREATE INDEX "StockBatch_productSupplierId_idx" ON "StockBatch"("productSupplierId");
CREATE INDEX "StockBatch_status_idx" ON "StockBatch"("status");
CREATE INDEX "StockBatch_expiryDate_idx" ON "StockBatch"("expiryDate");
CREATE INDEX "StockBatch_receivedDate_idx" ON "StockBatch"("receivedDate");

CREATE INDEX "PurchaseOrderItem_productSupplierId_idx" ON "PurchaseOrderItem"("productSupplierId");

-- Add foreign key constraints
ALTER TABLE "ProductSupplier" ADD CONSTRAINT "ProductSupplier_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ProductSupplier" ADD CONSTRAINT "ProductSupplier_supplierId_fkey" FOREIGN KEY ("supplierId") REFERENCES "Supplier"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "StockBatch" ADD CONSTRAINT "StockBatch_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "StockBatch" ADD CONSTRAINT "StockBatch_productSupplierId_fkey" FOREIGN KEY ("productSupplierId") REFERENCES "ProductSupplier"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "StockBatch" ADD CONSTRAINT "StockBatch_purchaseOrderId_fkey" FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "StockBatch" ADD CONSTRAINT "StockBatch_warehouseStockId_fkey" FOREIGN KEY ("warehouseStockId") REFERENCES "WarehouseStock"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "StockBatch" ADD CONSTRAINT "StockBatch_storeStockId_fkey" FOREIGN KEY ("storeStockId") REFERENCES "StoreStock"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "PurchaseOrderItem" ADD CONSTRAINT "PurchaseOrderItem_productSupplierId_fkey" FOREIGN KEY ("productSupplierId") REFERENCES "ProductSupplier"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "StockHistory" ADD CONSTRAINT "StockHistory_productSupplierId_fkey" FOREIGN KEY ("productSupplierId") REFERENCES "ProductSupplier"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "StockHistory" ADD CONSTRAINT "StockHistory_batchId_fkey" FOREIGN KEY ("batchId") REFERENCES "StockBatch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "StockAdjustment" ADD CONSTRAINT "StockAdjustment_batchId_fkey" FOREIGN KEY ("batchId") REFERENCES "StockBatch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "TransactionItem" ADD CONSTRAINT "TransactionItem_batchId_fkey" FOREIGN KEY ("batchId") REFERENCES "StockBatch"("id") ON DELETE SET NULL ON UPDATE CASCADE;
