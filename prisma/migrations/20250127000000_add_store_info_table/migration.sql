-- CreateTable
CREATE TABLE "StoreInfo" (
    "id" TEXT NOT NULL,
    "storeName" TEXT NOT NULL,
    "phone" TEXT,
    "address" TEXT,
    "email" TEXT,
    "website" TEXT,
    "taxId" TEXT,
    "logoUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StoreInfo_pkey" PRIMARY KEY ("id")
);

-- Create a unique constraint to ensure only one store info record exists
CREATE UNIQUE INDEX "StoreInfo_id_key" ON "StoreInfo"("id");

-- Insert default store information
INSERT INTO "StoreInfo" ("id", "storeName", "phone", "address", "email", "createdAt", "updatedAt") 
VALUES (
    'default-store',
    'Next POS Store',
    NULL,
    NULL,
    NULL,
    CURRENT_TIMES<PERSON><PERSON>,
    CURRENT_TIMESTAMP
);
