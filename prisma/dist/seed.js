"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var prisma_1 = require("@/generated/prisma");
var bcrypt = require("bcryptjs");
var prisma = new prisma_1.PrismaClient();
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var hashedPassword, superAdmin, cashierPassword, cashier, financePassword, financeAdmin, developerPassword, developer, cashDrawer, settings, _i, settings_1, setting;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('Starting database seeding...');
                    return [4 /*yield*/, bcrypt.hash('admin123', 10)];
                case 1:
                    hashedPassword = _a.sent();
                    return [4 /*yield*/, prisma.user.upsert({
                            where: { email: '<EMAIL>' },
                            update: {},
                            create: {
                                name: 'Super Admin',
                                email: '<EMAIL>',
                                password: hashedPassword,
                                role: 'SUPER_ADMIN',
                                active: true,
                            },
                        })];
                case 2:
                    superAdmin = _a.sent();
                    console.log('Created super admin user:', superAdmin.email);
                    return [4 /*yield*/, bcrypt.hash('cashier123', 10)];
                case 3:
                    cashierPassword = _a.sent();
                    return [4 /*yield*/, prisma.user.upsert({
                            where: { email: '<EMAIL>' },
                            update: {},
                            create: {
                                name: 'Cashier User',
                                email: '<EMAIL>',
                                password: cashierPassword,
                                role: 'CASHIER',
                                active: true,
                            },
                        })];
                case 4:
                    cashier = _a.sent();
                    console.log('Created cashier user:', cashier.email);
                    return [4 /*yield*/, bcrypt.hash('finance123', 10)];
                case 5:
                    financePassword = _a.sent();
                    return [4 /*yield*/, prisma.user.upsert({
                            where: { email: '<EMAIL>' },
                            update: {},
                            create: {
                                name: 'Finance Admin',
                                email: '<EMAIL>',
                                password: financePassword,
                                role: 'FINANCE_ADMIN',
                                active: true,
                            },
                        })];
                case 6:
                    financeAdmin = _a.sent();
                    console.log('Created finance admin user:', financeAdmin.email);
                    return [4 /*yield*/, bcrypt.hash('developer123', 10)];
                case 7:
                    developerPassword = _a.sent();
                    return [4 /*yield*/, prisma.user.upsert({
                            where: { email: '<EMAIL>' },
                            update: {},
                            create: {
                                name: 'Developer',
                                email: '<EMAIL>',
                                password: developerPassword,
                                role: 'DEVELOPER',
                                active: true,
                            },
                        })];
                case 8:
                    developer = _a.sent();
                    console.log('Created developer user:', developer.email);
                    return [4 /*yield*/, prisma.cashDrawer.create({
                            data: {
                                name: 'Main Drawer',
                                location: 'Front Counter',
                                isActive: true,
                            },
                        })];
                case 9:
                    cashDrawer = _a.sent();
                    console.log('Created cash drawer:', cashDrawer.name);
                    settings = [
                        { key: 'STORE_NAME', value: 'Next POS Store', description: 'Store name displayed in receipts and UI' },
                        { key: 'STORE_ADDRESS', value: '123 Main St, City, Country', description: 'Store address displayed in receipts' },
                        { key: 'STORE_PHONE', value: '****** 567 8900', description: 'Store phone number' },
                        { key: 'CURRENCY_SYMBOL', value: '$', description: 'Currency symbol used throughout the application' },
                        { key: 'TAX_RATE', value: '7.5', description: 'Default tax rate percentage' },
                        { key: 'RECEIPT_FOOTER', value: 'Thank you for shopping with us!', description: 'Message displayed at the bottom of receipts' },
                        { key: 'ENABLE_CHAT', value: 'true', description: 'Enable or disable the chat feature' },
                    ];
                    _i = 0, settings_1 = settings;
                    _a.label = 10;
                case 10:
                    if (!(_i < settings_1.length)) return [3 /*break*/, 13];
                    setting = settings_1[_i];
                    return [4 /*yield*/, prisma.systemSetting.upsert({
                            where: { key: setting.key },
                            update: { value: setting.value },
                            create: {
                                key: setting.key,
                                value: setting.value,
                                description: setting.description,
                            },
                        })];
                case 11:
                    _a.sent();
                    console.log("Created/updated setting: ".concat(setting.key));
                    _a.label = 12;
                case 12:
                    _i++;
                    return [3 /*break*/, 10];
                case 13:
                    console.log('Database seeding completed successfully!');
                    return [2 /*return*/];
            }
        });
    });
}
main()
    .catch(function (e) {
    console.error('Error during database seeding:', e);
    process.exit(1);
})
    .finally(function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, prisma.$disconnect()];
            case 1:
                _a.sent();
                return [2 /*return*/];
        }
    });
}); });
