// This script seeds the product table with fake data
const { PrismaClient } = require('../src/generated/prisma');
const { faker } = require('@faker-js/faker');

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed products...');

  try {
    // Get existing categories, units, and suppliers to reference
    const categories = await prisma.category.findMany();
    const units = await prisma.unit.findMany();
    const suppliers = await prisma.supplier.findMany();

    // Create default categories, units, and suppliers if none exist
    let defaultCategory;
    if (categories.length === 0) {
      console.log('No categories found. Creating default categories...');
      const defaultCategories = [
        { name: 'Electronics', description: 'Electronic devices and accessories' },
        { name: 'Food', description: 'Food and beverages' },
        { name: 'Clothing', description: 'Apparel and fashion items' },
        { name: 'Home', description: 'Home goods and furniture' },
        { name: 'Beauty', description: 'Beauty and personal care products' }
      ];

      for (const category of defaultCategories) {
        await prisma.category.create({ data: category });
      }

      // Refresh categories
      const updatedCategories = await prisma.category.findMany();
      defaultCategory = updatedCategories[0];
    } else {
      defaultCategory = categories[0];
    }

    let defaultUnit;
    if (units.length === 0) {
      console.log('No units found. Creating default units...');
      const defaultUnits = [
        { name: 'Piece', abbreviation: 'pc', description: 'Individual item' },
        { name: 'Kilogram', abbreviation: 'kg', description: 'Weight in kilograms' },
        { name: 'Liter', abbreviation: 'L', description: 'Volume in liters' },
        { name: 'Box', abbreviation: 'box', description: 'Box containing multiple items' },
        { name: 'Pair', abbreviation: 'pr', description: 'A pair of items' }
      ];

      for (const unit of defaultUnits) {
        await prisma.unit.create({ data: unit });
      }

      // Refresh units
      const updatedUnits = await prisma.unit.findMany();
      defaultUnit = updatedUnits[0];
    } else {
      defaultUnit = units[0];
    }

    let defaultSupplier;
    if (suppliers.length === 0) {
      console.log('No suppliers found. Creating default suppliers...');
      const defaultSuppliers = [
        {
          name: 'ABC Distributors',
          contactPerson: 'John Smith',
          phone: '************',
          email: '<EMAIL>',
          address: '123 Supply St, Warehouse District'
        },
        {
          name: 'Global Imports',
          contactPerson: 'Jane Doe',
          phone: '************',
          email: '<EMAIL>',
          address: '456 Import Ave, Business Park'
        },
        {
          name: 'Local Producers',
          contactPerson: 'Sam Johnson',
          phone: '************',
          email: '<EMAIL>',
          address: '789 Local Rd, Industrial Zone'
        }
      ];

      for (const supplier of defaultSuppliers) {
        await prisma.supplier.create({ data: supplier });
      }

      // Refresh suppliers
      const updatedSuppliers = await prisma.supplier.findMany();
      defaultSupplier = updatedSuppliers[0];
    } else {
      defaultSupplier = suppliers[0];
    }

    // Refresh all data
    const allCategories = await prisma.category.findMany();
    const allUnits = await prisma.unit.findMany();
    const allSuppliers = await prisma.supplier.findMany();

    // Generate 100 fake products
    const products = [];
    const skuSet = new Set(); // To ensure unique SKUs
    const barcodeSet = new Set(); // To ensure unique barcodes

    for (let i = 0; i < 100; i++) {
      // Generate a unique SKU
      let sku;
      do {
        sku = faker.string.alphanumeric(3).toUpperCase() + '-' +
              faker.number.int({ min: 1000, max: 9999 });
      } while (skuSet.has(sku));
      skuSet.add(sku);

      // Generate a unique barcode (EAN-13 format)
      let barcode;
      do {
        barcode = faker.string.numeric(13);
      } while (barcodeSet.has(barcode));
      barcodeSet.add(barcode);

      // Random category, unit, and supplier
      const category = faker.helpers.arrayElement(allCategories);
      const unit = faker.helpers.arrayElement(allUnits);
      const supplier = faker.helpers.arrayElement(allSuppliers);

      // Random prices
      const basePrice = parseFloat(faker.commerce.price({ min: 5, max: 500 }));
      const purchasePrice = basePrice * faker.number.float({ min: 0.5, max: 0.8 });
      const optionalPrice1 = basePrice * faker.number.float({ min: 0.85, max: 0.95 });
      const optionalPrice2 = basePrice * faker.number.float({ min: 0.75, max: 0.85 });

      // Random discount (30% chance of having a discount)
      const hasDiscount = faker.number.int(10) < 3;
      const discountType = hasDiscount ? faker.helpers.arrayElement(['FIXED', 'PERCENTAGE']) : null;
      const discountValue = hasDiscount
        ? (discountType === 'FIXED'
            ? faker.number.float({ min: 1, max: basePrice * 0.3, precision: 0.01 })
            : faker.number.float({ min: 5, max: 30, precision: 0.1 }))
        : null;

      // Random expiry date (null or future date)
      const hasExpiryDate = faker.number.int(10) < 5; // 50% chance of having expiry date
      const expiryDate = hasExpiryDate
        ? faker.date.future({ years: 2 })
        : null;

      // Create product object
      const product = {
        name: faker.commerce.productName(),
        description: faker.commerce.productDescription(),
        sku,
        barcode,
        categoryId: category.id,
        unitId: unit.id,
        supplierId: supplier.id,
        basePrice,
        purchasePrice,
        optionalPrice1,
        optionalPrice2,
        discountValue,
        discountType,
        expiryDate,
        imageUrl: null, // No images for now
        active: true,
      };

      products.push(product);
    }

    // Insert products in batches
    console.log(`Inserting ${products.length} products...`);
    for (let i = 0; i < products.length; i += 10) {
      const batch = products.slice(i, i + 10);
      await Promise.all(batch.map(product =>
        prisma.product.create({ data: product })
      ));
      console.log(`Inserted products ${i + 1} to ${Math.min(i + 10, products.length)}`);
    }

    console.log('Product seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding products:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
