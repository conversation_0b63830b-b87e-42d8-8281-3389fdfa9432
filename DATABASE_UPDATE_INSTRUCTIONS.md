# Database Update Instructions for Purchase Order Notification System

## Overview
The Purchase Order Approval Notification System requires database schema updates to add new fields to the Notification table and extend the NotificationType enum.

## Required Changes

### 1. Add New Columns to Notification Table
```sql
ALTER TABLE "Notification" ADD COLUMN "purchaseOrderId" TEXT;
ALTER TABLE "Notification" ADD COLUMN "actionUrl" TEXT;
ALTER TABLE "Notification" ADD COLUMN "metadata" JSONB;
```

### 2. Add Foreign Key Constraint
```sql
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_purchaseOrderId_fkey" 
FOREIGN KEY ("purchaseOrderId") REFERENCES "PurchaseOrder"("id") ON DELETE CASCADE;
```

### 3. Update NotificationType Enum
```sql
ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_APPROVAL';
ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_APPROVED';
ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_REJECTED';
ALTER TYPE "NotificationType" ADD VALUE 'PURCHASE_ORDER_RECEIVED';
```

## Alternative: Using Prisma Commands

### Option 1: Database Push (Recommended for Development)
```bash
npx prisma db push
```

### Option 2: Create and Apply Migration
```bash
npx prisma migrate dev --name add-po-notifications
```

### Option 3: Reset Database (WARNING: This will delete all data)
```bash
npx prisma migrate reset
```

## After Database Update

### 1. Regenerate Prisma Client
```bash
npx prisma generate
```

### 2. Re-enable Notification Triggers
Uncomment the notification trigger code in:
- `src/app/api/purchase-orders/route.ts`
- `src/app/api/purchase-orders/[id]/route.ts`
- `src/app/api/purchase-orders/[id]/receive/route.ts`

### 3. Test the System
1. Create a new Purchase Order
2. Check that approval notifications are created
3. Test the notification dropdown and dedicated page
4. Verify PO status change notifications work

## Current Status
- ✅ Application is stable with backward-compatible notification system
- ✅ Notification UI components are enhanced and ready
- ✅ API endpoints are backward-compatible
- ⏳ Database schema update pending
- ⏳ Notification triggers temporarily disabled

## Troubleshooting

### If Prisma Commands Fail
1. Check database connection in `.env` file
2. Ensure database server is running
3. Try running commands with `--force-reset` flag (WARNING: deletes data)
4. Manually execute SQL commands in database management tool

### If Application Still Has Errors
1. Check browser console for specific error messages
2. Verify all notification-related imports are correct
3. Ensure TypeScript types match database schema
4. Clear browser cache and restart development server

## Testing Checklist
- [ ] Application loads without errors
- [ ] Notification dropdown appears in header
- [ ] Notifications page accessible at `/notifications`
- [ ] Purchase Order creation works
- [ ] Purchase Order approval workflow functions
- [ ] Receiving workflow operates correctly
- [ ] Notifications are created for PO status changes
- [ ] Notifications can be marked as read
- [ ] PO-specific notification details display correctly
