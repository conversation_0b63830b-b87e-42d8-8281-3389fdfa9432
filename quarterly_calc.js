
const startDate = new Date('2025-05-31');
const endDate = new Date('2025-09-29');
const now = new Date('2025-06-01');

const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
const daysElapsed = Math.max(0, Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1);
const timeProgressPercentage = Math.min((daysElapsed / totalDays) * 100, 100);

console.log('=== QUARTERLY TARGET (G1) ===');
console.log('Period: May 31 - Sep 29, 2025');
console.log('Total Days:', totalDays);
console.log('Days Elapsed (as of June 1):', daysElapsed);
console.log('Time Progress:', timeProgressPercentage.toFixed(2) + '%');

const targetAmount = 250000000; // 250M IDR
const actualRevenue = 4081000;   // 4.081M IDR
const expectedRevenue = (targetAmount * timeProgressPercentage) / 100;
const performanceRatio = expectedRevenue > 0 ? (actualRevenue / expectedRevenue) : 0;

console.log('Target Amount: Rp', targetAmount.toLocaleString());
console.log('Expected by now: Rp', expectedRevenue.toFixed(0));
console.log('Actual Revenue: Rp', actualRevenue.toLocaleString());
console.log('Performance Ratio:', performanceRatio.toFixed(2) + 'x');

let status = 'on-track';
if (performanceRatio < 0.7) status = 'critical';
else if (performanceRatio < 0.9) status = 'behind';

console.log('Status:', status.toUpperCase());

