#!/bin/bash

# Test script for Phase 3 Enhanced Security Measures
echo "🚀 Starting Phase 3 Enhanced Security Measures Testing"
echo "======================================================"

BASE_URL="http://localhost:3000"
EMAIL="<EMAIL>"
PASSWORD="password123"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_TOTAL=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_status="$3"
    
    echo -e "\n${BLUE}🧪 Testing: $test_name${NC}"
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    # Run the test command and capture response
    response=$(eval "$test_command")
    status=$?
    
    if [ $status -eq $expected_status ]; then
        echo -e "${GREEN}✅ PASSED${NC}: $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo "Response: $response"
    else
        echo -e "${RED}❌ FAILED${NC}: $test_name"
        echo "Expected status: $expected_status, Got: $status"
        echo "Response: $response"
    fi
}

# Test 1: Login and get session token
echo -e "\n${YELLOW}🔐 Step 1: Testing login and session creation${NC}"
login_response=$(curl -s -c cookies.txt -X POST "$BASE_URL/api/login" \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"$EMAIL\",\"password\":\"$PASSWORD\"}")

if echo "$login_response" | grep -q "success"; then
    echo -e "${GREEN}✅ Login successful${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ Login failed${NC}"
    echo "Response: $login_response"
    exit 1
fi
TESTS_TOTAL=$((TESTS_TOTAL + 1))

# Test 2: Test security logging endpoint
echo -e "\n${YELLOW}🛡️ Step 2: Testing security logging${NC}"
security_response=$(curl -s -b cookies.txt -X POST "$BASE_URL/api/security/test" \
    -H "Content-Type: application/json")

if echo "$security_response" | grep -q "success"; then
    echo -e "${GREEN}✅ Security logging test passed${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ Security logging test failed${NC}"
    echo "Response: $security_response"
fi
TESTS_TOTAL=$((TESTS_TOTAL + 1))

# Test 3: Test session timeout endpoint
echo -e "\n${YELLOW}⏰ Step 3: Testing session timeout${NC}"
timeout_response=$(curl -s -b cookies.txt "$BASE_URL/api/session-timeout/drawer_session")
timeout_status=$?

if [ $timeout_status -eq 0 ] || echo "$timeout_response" | grep -q "No active session timeout"; then
    echo -e "${GREEN}✅ Session timeout endpoint working${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ Session timeout test failed${NC}"
    echo "Response: $timeout_response"
fi
TESTS_TOTAL=$((TESTS_TOTAL + 1))

# Test 4: Get current drawer session
echo -e "\n${YELLOW}📦 Step 4: Getting current drawer session${NC}"
drawer_response=$(curl -s -b cookies.txt "$BASE_URL/api/drawer-sessions/current")

if echo "$drawer_response" | grep -q "session"; then
    echo -e "${GREEN}✅ Found active drawer session${NC}"
    
    # Extract drawer session ID
    drawer_id=$(echo "$drawer_response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    echo "Drawer Session ID: $drawer_id"
    
    if [ -n "$drawer_id" ]; then
        # Test 5: Test normal discrepancy (under $100)
        echo -e "\n${YELLOW}💰 Step 5: Testing normal discrepancy (under threshold)${NC}"
        normal_response=$(curl -s -b cookies.txt -X POST "$BASE_URL/api/drawer-sessions/$drawer_id/close" \
            -H "Content-Type: application/json" \
            -d '{"actualClosingBalance":150,"notes":"Test normal discrepancy"}')
        
        if echo "$normal_response" | grep -q "success\|session"; then
            echo -e "${GREEN}✅ Normal discrepancy test passed${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            echo -e "${RED}❌ Normal discrepancy test failed${NC}"
            echo "Response: $normal_response"
        fi
        TESTS_TOTAL=$((TESTS_TOTAL + 1))
        
        # Test 6: Test large discrepancy (over $100) - should require re-auth
        echo -e "\n${YELLOW}🚨 Step 6: Testing large discrepancy (over threshold)${NC}"
        large_response=$(curl -s -b cookies.txt -X POST "$BASE_URL/api/drawer-sessions/$drawer_id/close" \
            -H "Content-Type: application/json" \
            -d '{"actualClosingBalance":250,"notes":"Test large discrepancy"}')
        
        if echo "$large_response" | grep -q "requireReAuth\|Re-authentication required"; then
            echo -e "${GREEN}✅ Large discrepancy detection working${NC}"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            
            # Test 7: Test large discrepancy with re-auth
            echo -e "\n${YELLOW}🔑 Step 7: Testing large discrepancy with re-authentication${NC}"
            reauth_response=$(curl -s -b cookies.txt -X POST "$BASE_URL/api/drawer-sessions/$drawer_id/close" \
                -H "Content-Type: application/json" \
                -d "{\"actualClosingBalance\":250,\"notes\":\"Test large discrepancy with re-auth\",\"reAuthPassword\":\"$PASSWORD\"}")
            
            if echo "$reauth_response" | grep -q "success\|session"; then
                echo -e "${GREEN}✅ Re-authentication test passed${NC}"
                TESTS_PASSED=$((TESTS_PASSED + 1))
            else
                echo -e "${RED}❌ Re-authentication test failed${NC}"
                echo "Response: $reauth_response"
            fi
            TESTS_TOTAL=$((TESTS_TOTAL + 1))
        else
            echo -e "${RED}❌ Large discrepancy detection failed${NC}"
            echo "Response: $large_response"
        fi
        TESTS_TOTAL=$((TESTS_TOTAL + 1))
    fi
else
    echo -e "${YELLOW}ℹ️ No active drawer session found (expected if no drawer is open)${NC}"
    echo "Response: $drawer_response"
fi

# Test 8: Test session timeout activity update
echo -e "\n${YELLOW}🔄 Step 8: Testing session activity update${NC}"
activity_response=$(curl -s -b cookies.txt -X POST "$BASE_URL/api/session-timeout/drawer_session/activity")

if echo "$activity_response" | grep -q "success"; then
    echo -e "${GREEN}✅ Session activity update working${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ Session activity update failed${NC}"
    echo "Response: $activity_response"
fi
TESTS_TOTAL=$((TESTS_TOTAL + 1))

# Clean up
rm -f cookies.txt

# Print summary
echo -e "\n${BLUE}📊 Test Results Summary${NC}"
echo "========================"
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Total:  ${BLUE}$TESTS_TOTAL${NC}"

if [ $TESTS_PASSED -eq $TESTS_TOTAL ]; then
    echo -e "\n${GREEN}🎉 All Phase 3 Enhanced Security Measures are working correctly!${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️ Some tests failed. Please check the implementation.${NC}"
    echo -e "Success Rate: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
    exit 1
fi
